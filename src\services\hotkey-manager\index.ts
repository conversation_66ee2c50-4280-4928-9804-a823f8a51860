/**
 * Hotkey Manager module exports
 * Global hotkey registration and management capabilities
 */

// Core classes
export { HotkeyManager, getHotkeyManager, initializeHotkeyManager } from './HotkeyManager';
export { PlatformAdapterFactory } from './PlatformAdapterFactory';
export { WindowsPlatformAdapter } from './WindowsPlatformAdapter';
export { MacOSPlatformAdapter } from './MacOSPlatformAdapter';
export { LinuxPlatformAdapter } from './LinuxPlatformAdapter';
export { ConflictResolver } from './ConflictResolver';
export { HotkeyAnalyticsManager } from './HotkeyAnalyticsManager';
export { AcceleratorParser } from './AcceleratorParser';

// Types and interfaces
export * from './types';

// Re-export commonly used types for convenience
export type {
  IHotkeyManager,
  IPlatformAdapter,
  HotkeyManagerConfig,
  HotkeyDefinition,
  HotkeyRegistration,
  ActionHandler,
  ActionContext,
  ConflictResult,
  HotkeyAnalytics,
  ParsedAccelerator
} from './types';

// Convenience functions
export const createHotkeyManager = async (config?: any): Promise<any> => {
  return initializeHotkeyManager(config);
};

export const getDefaultHotkeyManager = (): any => {
  return getHotkeyManager();
};

// Hotkey management functions
export const registerHotkey = async (hotkey: any): Promise<boolean> => {
  return getHotkeyManager().registerHotkey(hotkey);
};

export const unregisterHotkey = async (accelerator: string): Promise<boolean> => {
  return getHotkeyManager().unregisterHotkey(accelerator);
};

export const updateHotkey = async (oldAccelerator: string, newAccelerator: string): Promise<boolean> => {
  return getHotkeyManager().updateHotkey(oldAccelerator, newAccelerator);
};

export const registerAction = async (actionId: string, handler: any): Promise<void> => {
  return getHotkeyManager().registerAction(actionId, handler);
};

export const triggerAction = async (actionId: string, context?: any): Promise<void> => {
  return getHotkeyManager().triggerAction(actionId, context);
};

export const getRegisteredHotkeys = (): any[] => {
  return getHotkeyManager().getRegisteredHotkeys();
};

export const isHotkeyAvailable = (accelerator: string): boolean => {
  return getHotkeyManager().isHotkeyAvailable(accelerator);
};

export const checkConflicts = async (accelerator: string): Promise<any> => {
  return getHotkeyManager().checkConflicts(accelerator);
};

// Analytics functions
export const getHotkeyAnalytics = (): any => {
  return getHotkeyManager().getAnalytics();
};

// Configuration functions
export const updateHotkeyConfig = (config: any): void => {
  return getHotkeyManager().updateConfig(config);
};

export const getHotkeyConfig = (): any => {
  return getHotkeyManager().getConfig();
};

// Built-in actions
export const BUILT_IN_ACTIONS = {
  'show-floating-window': {
    execute: async () => {
      console.log('Show floating window action triggered');
    },
    description: 'Show floating prompt window',
    category: 'Window Management'
  },
  'capture-screen': {
    execute: async () => {
      console.log('Capture screen action triggered');
    },
    description: 'Capture screen and extract text',
    category: 'Context Extraction'
  },
  'start-voice-input': {
    execute: async () => {
      console.log('Start voice input action triggered');
    },
    description: 'Start voice recording',
    category: 'Voice Input'
  },
  'show-prompt-library': {
    execute: async () => {
      console.log('Show prompt library action triggered');
    },
    description: 'Show prompt library',
    category: 'Prompt Management'
  },
  'quick-search': {
    execute: async () => {
      console.log('Quick search action triggered');
    },
    description: 'Quick search prompts',
    category: 'Search'
  },
  'enhance-clipboard': {
    execute: async () => {
      console.log('Enhance clipboard action triggered');
    },
    description: 'Enhance clipboard content',
    category: 'Prompt Enhancement'
  },
  'show-help': {
    execute: async () => {
      console.log('Show help action triggered');
    },
    description: 'Show help and shortcuts',
    category: 'Help'
  },
  'quit-application': {
    execute: async () => {
      console.log('Quit application action triggered');
    },
    description: 'Quit application',
    category: 'Application'
  }
};

// Default hotkeys
export const DEFAULT_HOTKEYS = [
  {
    accelerator: 'Ctrl+Shift+P',
    actionId: 'show-floating-window',
    description: 'Show floating prompt window',
    category: 'Window Management',
    enabled: true,
    global: true
  },
  {
    accelerator: 'Ctrl+Shift+O',
    actionId: 'capture-screen',
    description: 'Capture screen and extract text',
    category: 'Context Extraction',
    enabled: true,
    global: true
  },
  {
    accelerator: 'Ctrl+Shift+V',
    actionId: 'start-voice-input',
    description: 'Start voice recording',
    category: 'Voice Input',
    enabled: true,
    global: true
  },
  {
    accelerator: 'Ctrl+Shift+L',
    actionId: 'show-prompt-library',
    description: 'Show prompt library',
    category: 'Prompt Management',
    enabled: true,
    global: true
  },
  {
    accelerator: 'Ctrl+Shift+F',
    actionId: 'quick-search',
    description: 'Quick search prompts',
    category: 'Search',
    enabled: true,
    global: true
  },
  {
    accelerator: 'Ctrl+Shift+E',
    actionId: 'enhance-clipboard',
    description: 'Enhance clipboard content',
    category: 'Prompt Enhancement',
    enabled: true,
    global: true
  },
  {
    accelerator: 'Ctrl+Shift+H',
    actionId: 'show-help',
    description: 'Show help and shortcuts',
    category: 'Help',
    enabled: true,
    global: true
  }
];

// Utility functions
export const validateHotkeyDefinition = (hotkey: any): boolean => {
  try {
    if (!hotkey.accelerator || hotkey.accelerator.trim().length === 0) {
      return false;
    }
    if (!hotkey.actionId || hotkey.actionId.trim().length === 0) {
      return false;
    }
    if (!hotkey.description || hotkey.description.trim().length === 0) {
      return false;
    }
    return true;
  } catch {
    return false;
  }
};

export const parseAccelerator = (accelerator: string): any => {
  const parser = new AcceleratorParser();
  return parser.parse(accelerator);
};

export const normalizeAccelerator = (accelerator: string): string => {
  const parser = new AcceleratorParser();
  return parser.normalize(accelerator);
};

export const validateAccelerator = (accelerator: string): boolean => {
  const parser = new AcceleratorParser();
  return parser.validate(accelerator);
};

// Configuration helpers
export const getDefaultHotkeyConfig = () => ({
  platform: process.platform as any,
  enableGlobalHotkeys: true,
  enableConflictDetection: true,
  enableAnalytics: true,
  maxHotkeys: 50,
  defaultCategory: 'General',
  autoResolveConflicts: false,
  hotkeyTimeout: 5000
});

export const createHotkeyDefinition = (
  accelerator: string,
  actionId: string,
  description: string,
  category: string = 'General'
): any => ({
  accelerator,
  actionId,
  description,
  category,
  enabled: true,
  global: true,
  priority: 0
});

export const createActionHandler = (
  execute: (context?: any) => Promise<void>,
  description: string,
  canExecute?: (context?: any) => boolean
): any => ({
  execute,
  description,
  canExecute
});

// Platform detection
export const getCurrentPlatform = (): string => {
  return process.platform;
};

export const getSupportedPlatforms = (): string[] => {
  return ['win32', 'darwin', 'linux'];
};

export const isPlatformSupported = (platform: string): boolean => {
  return getSupportedPlatforms().includes(platform);
};

// Error handling helpers
export const isHotkeyManagerError = (error: any): boolean => {
  return error && (
    error.name === 'HotkeyManagerError' ||
    error.name === 'HotkeyRegistrationError' ||
    error.name === 'HotkeyConflictError' ||
    error.name === 'ActionExecutionError'
  );
};

export const getErrorMessage = (error: any): string => {
  if (isHotkeyManagerError(error)) {
    return error.message;
  }
  return 'An unknown hotkey manager error occurred';
};
