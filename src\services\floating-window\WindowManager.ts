/**
 * Window Manager
 * Manages multiple floating windows
 */

import { EventEmitter } from 'events';
import {
  IWindowManager,
  IFloatingWindow,
  FloatingWindowConfig,
  WindowShowOptions,
  WindowCreationError,
  WindowOperationError
} from './types';
import { FloatingWindow } from './FloatingWindow';
import { LoggerFactory } from '../../core/logger';

const logger = LoggerFactory.getInstance().getLogger('WindowManager');

/**
 * Window Manager implementation
 */
export class WindowManager extends EventEmitter implements IWindowManager {
  private windows: Map<string, IFloatingWindow> = new Map();
  private nextWindowId: number = 1;
  private defaultConfig: FloatingWindowConfig;

  constructor(defaultConfig?: Partial<FloatingWindowConfig>) {
    super();
    
    this.defaultConfig = {
      window: {
        width: 400,
        height: 300,
        minWidth: 200,
        minHeight: 150,
        alwaysOnTop: true,
        resizable: true,
        movable: true,
        minimizable: true,
        maximizable: true,
        closable: true,
        skipTaskbar: true,
        transparent: false,
        opacity: 1.0,
        ...defaultConfig?.window
      },
      ui: {
        theme: 'light',
        accentColor: '#007bff',
        fontSize: 14,
        fontFamily: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif',
        borderRadius: 8,
        showTitleBar: true,
        showControls: true,
        ...defaultConfig?.ui
      },
      behavior: {
        autoHide: false,
        autoHideDelay: 3000,
        stayOnTop: true,
        followCursor: false,
        snapToEdges: false,
        fadeInOut: true,
        rememberPosition: true,
        rememberSize: true,
        ...defaultConfig?.behavior
      },
      hotkeys: {
        show: 'Ctrl+Shift+P',
        hide: 'Escape',
        toggle: 'Ctrl+Shift+P',
        focus: 'Ctrl+Shift+F',
        ...defaultConfig?.hotkeys
      }
    };
  }

  /**
   * Create a new floating window
   */
  async createWindow(config?: Partial<FloatingWindowConfig>): Promise<IFloatingWindow> {
    try {
      const windowId = `window-${this.nextWindowId++}`;
      const mergedConfig = this.mergeConfig(config);
      
      const window = new FloatingWindow(windowId, mergedConfig);
      await window.initialize();
      
      this.windows.set(windowId, window);
      
      // Setup window event handlers
      this.setupWindowEventHandlers(window);

      logger.info('Floating window created', { windowId });
      
      this.emit('window-created', { windowId, window });
      
      return window;
    } catch (error) {
      logger.error('Failed to create floating window', { error });
      throw new WindowCreationError('Failed to create floating window', error);
    }
  }

  /**
   * Get window by ID
   */
  getWindow(id: string): IFloatingWindow | null {
    return this.windows.get(id) || null;
  }

  /**
   * Get all windows
   */
  getAllWindows(): IFloatingWindow[] {
    return Array.from(this.windows.values());
  }

  /**
   * Close window by ID
   */
  async closeWindow(id: string): Promise<void> {
    try {
      const window = this.windows.get(id);
      if (!window) {
        logger.warn('Window not found for closing', { id });
        return;
      }

      await window.close();
      this.windows.delete(id);
      
      logger.info('Window closed', { id });
      this.emit('window-closed', { windowId: id });
    } catch (error) {
      logger.error('Failed to close window', { error, id });
      throw new WindowOperationError('Failed to close window', error);
    }
  }

  /**
   * Close all windows
   */
  async closeAllWindows(): Promise<void> {
    try {
      const windowIds = Array.from(this.windows.keys());
      
      for (const id of windowIds) {
        await this.closeWindow(id);
      }
      
      logger.info('All windows closed', { count: windowIds.length });
      this.emit('all-windows-closed');
    } catch (error) {
      logger.error('Failed to close all windows', { error });
      throw new WindowOperationError('Failed to close all windows', error);
    }
  }

  /**
   * Show window by ID
   */
  async showWindow(id: string, options?: WindowShowOptions): Promise<void> {
    try {
      const window = this.windows.get(id);
      if (!window) {
        throw new WindowOperationError(`Window not found: ${id}`);
      }

      await window.show(options);
      
      logger.debug('Window shown', { id });
    } catch (error) {
      logger.error('Failed to show window', { error, id });
      throw new WindowOperationError('Failed to show window', error);
    }
  }

  /**
   * Hide window by ID
   */
  async hideWindow(id: string): Promise<void> {
    try {
      const window = this.windows.get(id);
      if (!window) {
        throw new WindowOperationError(`Window not found: ${id}`);
      }

      await window.hide();
      
      logger.debug('Window hidden', { id });
    } catch (error) {
      logger.error('Failed to hide window', { error, id });
      throw new WindowOperationError('Failed to hide window', error);
    }
  }

  /**
   * Focus window by ID
   */
  async focusWindow(id: string): Promise<void> {
    try {
      const window = this.windows.get(id);
      if (!window) {
        throw new WindowOperationError(`Window not found: ${id}`);
      }

      await window.focus();
      
      logger.debug('Window focused', { id });
    } catch (error) {
      logger.error('Failed to focus window', { error, id });
      throw new WindowOperationError('Failed to focus window', error);
    }
  }

  /**
   * Get visible windows
   */
  getVisibleWindows(): IFloatingWindow[] {
    return this.getAllWindows().filter(window => window.isVisible());
  }

  /**
   * Hide all windows
   */
  async hideAllWindows(): Promise<void> {
    try {
      const visibleWindows = this.getVisibleWindows();
      
      for (const window of visibleWindows) {
        await window.hide();
      }
      
      logger.info('All windows hidden', { count: visibleWindows.length });
      this.emit('all-windows-hidden');
    } catch (error) {
      logger.error('Failed to hide all windows', { error });
      throw new WindowOperationError('Failed to hide all windows', error);
    }
  }

  /**
   * Show all windows
   */
  async showAllWindows(): Promise<void> {
    try {
      const allWindows = this.getAllWindows();
      
      for (const window of allWindows) {
        if (!window.isVisible()) {
          await window.show();
        }
      }
      
      logger.info('All windows shown', { count: allWindows.length });
      this.emit('all-windows-shown');
    } catch (error) {
      logger.error('Failed to show all windows', { error });
      throw new WindowOperationError('Failed to show all windows', error);
    }
  }

  /**
   * Get window count
   */
  getWindowCount(): number {
    return this.windows.size;
  }

  /**
   * Update default configuration
   */
  updateDefaultConfig(config: Partial<FloatingWindowConfig>): void {
    this.defaultConfig = this.mergeConfig(config);
    logger.info('Default window configuration updated');
  }

  /**
   * Get default configuration
   */
  getDefaultConfig(): FloatingWindowConfig {
    return { ...this.defaultConfig };
  }

  /**
   * Merge configuration with defaults
   */
  private mergeConfig(config?: Partial<FloatingWindowConfig>): FloatingWindowConfig {
    if (!config) {
      return { ...this.defaultConfig };
    }

    return {
      window: { ...this.defaultConfig.window, ...config.window },
      ui: { ...this.defaultConfig.ui, ...config.ui },
      behavior: { ...this.defaultConfig.behavior, ...config.behavior },
      hotkeys: { ...this.defaultConfig.hotkeys, ...config.hotkeys }
    };
  }

  /**
   * Setup event handlers for a window
   */
  private setupWindowEventHandlers(window: IFloatingWindow): void {
    // Forward window events
    window.on('window-shown', (data) => this.emit('window-shown', data));
    window.on('window-hidden', (data) => this.emit('window-hidden', data));
    window.on('window-focused', (data) => this.emit('window-focused', data));
    window.on('window-blurred', (data) => this.emit('window-blurred', data));
    window.on('window-moved', (data) => this.emit('window-moved', data));
    window.on('window-resized', (data) => this.emit('window-resized', data));
    window.on('content-changed', (data) => this.emit('content-changed', data));
    
    // Handle window destruction
    window.on('window-destroyed', (data) => {
      const windowId = data.windowId;
      this.windows.delete(windowId);
      this.emit('window-destroyed', data);
    });
  }

  /**
   * Cleanup all resources
   */
  async cleanup(): Promise<void> {
    try {
      await this.closeAllWindows();
      this.removeAllListeners();
      
      logger.info('Window manager cleaned up');
    } catch (error) {
      logger.error('Error during window manager cleanup', { error });
    }
  }
}

// Global instance management
let globalWindowManager: WindowManager | null = null;

export const getWindowManager = (): WindowManager => {
  if (!globalWindowManager) {
    globalWindowManager = new WindowManager();
  }
  return globalWindowManager;
};

export const initializeWindowManager = (config?: Partial<FloatingWindowConfig>): WindowManager => {
  globalWindowManager = new WindowManager(config);
  return globalWindowManager;
};
