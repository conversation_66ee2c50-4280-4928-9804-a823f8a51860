/**
 * Main AI Service implementation
 * Coordinates multiple AI providers with fallback and caching
 */

import { EventEmitter } from 'events';
import { 
  IAIService, 
  IAIProvider, 
  AIProvider, 
  AIModel, 
  AIRequest, 
  AIResponse, 
  AIStreamChunk, 
  AIContext, 
  AIServiceConfig,
  AIProviderError,
  AIRateLimitError
} from './types';
import { OpenAIProvider } from './providers/OpenAIProvider';
import { LoggerFactory } from '../../core/logger';
import { getConfigManager } from '../../core/config';

const logger = LoggerFactory.getInstance().getLogger('AIService');

/**
 * Response cache entry
 */
interface CacheEntry {
  response: AIResponse;
  timestamp: Date;
  expiresAt: Date;
}

/**
 * Main AI Service implementation
 */
export class AIService extends EventEmitter implements IAIService {
  private providers: Map<AIProvider, IAIProvider> = new Map();
  private config: AIServiceConfig;
  private responseCache: Map<string, CacheEntry> = new Map();
  private isInitialized: boolean = false;

  constructor(config?: Partial<AIServiceConfig>) {
    super();
    
    this.config = {
      defaultProvider: 'openai',
      fallbackProviders: ['openai'],
      enableCaching: true,
      cacheTimeout: 300000, // 5 minutes
      enableRateLimiting: true,
      enableUsageTracking: true,
      enableErrorRetry: true,
      maxRetries: 3,
      retryDelay: 1000,
      timeout: 30000,
      enableLogging: true,
      logLevel: 'info',
      ...config
    };
  }

  /**
   * Initialize the AI service
   */
  async initialize(): Promise<void> {
    if (this.isInitialized) {
      return;
    }

    try {
      await this.initializeProviders();
      this.setupCacheCleanup();
      this.isInitialized = true;
      
      logger.info('AI Service initialized successfully', {
        providers: Array.from(this.providers.keys()),
        defaultProvider: this.config.defaultProvider
      });
    } catch (error) {
      logger.error('Failed to initialize AI Service', error);
      throw error;
    }
  }

  /**
   * Register AI provider
   */
  registerProvider(provider: IAIProvider): void {
    this.providers.set(provider.name, provider);
    
    // Set up event listeners
    provider.on('usage', (data) => {
      this.emit('usage', { ...data, provider: provider.name });
    });
    
    provider.on('error', (data) => {
      this.emit('error', { ...data, provider: provider.name });
    });
    
    logger.info(`AI Provider ${provider.name} registered`);
  }

  /**
   * Get AI provider
   */
  getProvider(name: AIProvider): IAIProvider | null {
    return this.providers.get(name) || null;
  }

  /**
   * Get available providers
   */
  getAvailableProviders(): AIProvider[] {
    return Array.from(this.providers.keys());
  }

  /**
   * Set default provider
   */
  setDefaultProvider(provider: AIProvider): void {
    if (!this.providers.has(provider)) {
      throw new Error(`Provider ${provider} is not registered`);
    }
    
    (this.config as any).defaultProvider = provider;
    logger.info(`Default provider set to ${provider}`);
  }

  /**
   * Get available models
   */
  async getAvailableModels(): Promise<AIModel[]> {
    const allModels: AIModel[] = [];
    
    for (const provider of Array.from(this.providers.values())) {
      try {
        const models = await provider.getModels();
        allModels.push(...models);
      } catch (error) {
        logger.warn(`Failed to get models from ${provider.name}`, error);
      }
    }
    
    return allModels;
  }

  /**
   * Get specific model
   */
  async getModel(modelId: string, provider?: AIProvider): Promise<AIModel | null> {
    if (provider) {
      const providerInstance = this.getProvider(provider);
      return providerInstance ? await providerInstance.getModel(modelId) : null;
    }
    
    // Search all providers
    for (const providerInstance of Array.from(this.providers.values())) {
      try {
        const model = await providerInstance.getModel(modelId);
        if (model) return model;
      } catch (error) {
        logger.debug(`Model ${modelId} not found in ${providerInstance.name}`);
      }
    }
    
    return null;
  }

  /**
   * Complete AI request
   */
  async complete(request: AIRequest, provider?: AIProvider): Promise<AIResponse> {
    this.ensureInitialized();
    
    // Check cache first
    if (this.config.enableCaching) {
      const cached = this.getCachedResponse(request);
      if (cached) {
        logger.debug('Returning cached response');
        return cached;
      }
    }
    
    const providersToTry = this.getProvidersToTry(provider);
    let lastError: Error | null = null;
    
    for (const providerName of providersToTry) {
      const providerInstance = this.getProvider(providerName);
      if (!providerInstance) continue;
      
      try {
        const response = await this.executeWithRetry(
          () => providerInstance.complete(request),
          providerName
        );
        
        // Cache successful response
        if (this.config.enableCaching) {
          this.cacheResponse(request, response);
        }
        
        this.emit('completion', {
          provider: providerName,
          model: response.model,
          tokens: response.usage.totalTokens,
          cost: response.usage.cost,
          latency: response.latency
        });
        
        return response;
      } catch (error) {
        lastError = error as Error;
        logger.warn(`Request failed with ${providerName}`, error);
        
        // Don't try fallback for certain errors
        if (error instanceof AIProviderError && 
            ['INVALID_API_KEY', 'QUOTA_EXCEEDED'].includes(error.code || '')) {
          break;
        }
      }
    }
    
    throw lastError || new Error('All providers failed');
  }

  /**
   * Stream AI request
   */
  async* stream(request: AIRequest, provider?: AIProvider): AsyncIterable<AIStreamChunk> {
    this.ensureInitialized();
    
    const providersToTry = this.getProvidersToTry(provider);
    let lastError: Error | null = null;
    
    for (const providerName of providersToTry) {
      const providerInstance = this.getProvider(providerName);
      if (!providerInstance) continue;
      
      try {
        const stream = providerInstance.stream(request);
        
        for await (const chunk of stream) {
          this.emit('stream-chunk', {
            provider: providerName,
            model: chunk.model,
            chunk
          });
          
          yield chunk;
        }
        
        return; // Success, exit
      } catch (error) {
        lastError = error as Error;
        logger.warn(`Stream failed with ${providerName}`, error);
        
        // Don't try fallback for certain errors
        if (error instanceof AIProviderError && 
            ['INVALID_API_KEY', 'QUOTA_EXCEEDED'].includes(error.code || '')) {
          break;
        }
      }
    }
    
    throw lastError || new Error('All providers failed');
  }

  /**
   * Create AI context
   */
  createContext(options?: Partial<AIContext>): AIContext {
    return {
      conversationId: this.generateId(),
      sessionId: this.generateId(),
      history: [],
      maxHistoryLength: 10,
      ...options
    };
  }

  /**
   * Update AI context
   */
  updateContext(context: AIContext, updates: Partial<AIContext>): AIContext {
    return { ...context, ...updates };
  }

  /**
   * Estimate tokens
   */
  async estimateTokens(text: string, model?: string, provider?: AIProvider): Promise<number> {
    const providerName = provider || this.config.defaultProvider;
    const providerInstance = this.getProvider(providerName);
    
    if (!providerInstance) {
      // Fallback estimation
      return Math.ceil(text.length / 4);
    }
    
    return providerInstance.estimateTokens(text, model);
  }

  /**
   * Estimate cost
   */
  async estimateCost(tokens: number, model?: string, provider?: AIProvider): Promise<number> {
    if (!model) return 0;
    
    const modelInfo = await this.getModel(model, provider);
    if (!modelInfo) return 0;
    
    // Assume equal split between input and output tokens
    const inputTokens = Math.ceil(tokens * 0.7);
    const outputTokens = Math.ceil(tokens * 0.3);
    
    const inputCost = (inputTokens / 1000) * modelInfo.capabilities.costPer1kTokens.input;
    const outputCost = (outputTokens / 1000) * modelInfo.capabilities.costPer1kTokens.output;
    
    return inputCost + outputCost;
  }

  /**
   * Configure service
   */
  configure(config: Partial<AIServiceConfig>): void {
    this.config = { ...this.config, ...config };
    logger.info('AI Service configuration updated', config);
  }

  /**
   * Get configuration
   */
  getConfig(): AIServiceConfig {
    return { ...this.config };
  }

  /**
   * Initialize providers from configuration
   */
  private async initializeProviders(): Promise<void> {
    const configManager = getConfigManager();
    
    // Initialize OpenAI provider
    const openaiConfig = {
      provider: 'openai' as AIProvider,
      apiKey: configManager.get('ai.openai.apiKey'),
      defaultModel: configManager.get('ai.openai.model') || 'gpt-3.5-turbo',
      enabled: !!configManager.get('ai.openai.apiKey'),
      timeout: this.config.timeout,
      retries: this.config.maxRetries
    };
    
    if (openaiConfig.enabled) {
      const openaiProvider = new OpenAIProvider(openaiConfig);
      await openaiProvider.initialize();
      this.registerProvider(openaiProvider);
    }
    
    // TODO: Initialize other providers (Anthropic, Google, etc.)
  }

  /**
   * Get providers to try for request
   */
  private getProvidersToTry(preferredProvider?: AIProvider): AIProvider[] {
    if (preferredProvider && this.providers.has(preferredProvider)) {
      return [preferredProvider];
    }
    
    const providers = [this.config.defaultProvider, ...this.config.fallbackProviders];
    return [...new Set(providers)].filter(p => this.providers.has(p));
  }

  /**
   * Execute request with retry logic
   */
  private async executeWithRetry<T>(
    operation: () => Promise<T>,
    provider: AIProvider
  ): Promise<T> {
    let lastError: Error;
    
    for (let attempt = 1; attempt <= this.config.maxRetries; attempt++) {
      try {
        return await operation();
      } catch (error) {
        lastError = error as Error;
        
        // Don't retry certain errors
        if (error instanceof AIProviderError && 
            ['INVALID_API_KEY', 'QUOTA_EXCEEDED', 'MODEL_NOT_FOUND'].includes(error.code || '')) {
          throw error;
        }
        
        // Don't retry on last attempt
        if (attempt === this.config.maxRetries) {
          break;
        }
        
        // Wait before retry (with exponential backoff)
        const delay = this.config.retryDelay * Math.pow(2, attempt - 1);
        await new Promise(resolve => setTimeout(resolve, delay));
        
        logger.debug(`Retrying request (attempt ${attempt + 1}/${this.config.maxRetries})`, {
          provider,
          delay,
          error: error instanceof Error ? error.message : String(error)
        });
      }
    }
    
    throw lastError!;
  }

  /**
   * Generate cache key for request
   */
  private generateCacheKey(request: AIRequest): string {
    const key = {
      messages: request.messages,
      options: request.options
    };
    return Buffer.from(JSON.stringify(key)).toString('base64');
  }

  /**
   * Get cached response
   */
  private getCachedResponse(request: AIRequest): AIResponse | null {
    const key = this.generateCacheKey(request);
    const entry = this.responseCache.get(key);
    
    if (!entry || entry.expiresAt < new Date()) {
      if (entry) {
        this.responseCache.delete(key);
      }
      return null;
    }
    
    return entry.response;
  }

  /**
   * Cache response
   */
  private cacheResponse(request: AIRequest, response: AIResponse): void {
    const key = this.generateCacheKey(request);
    const expiresAt = new Date(Date.now() + this.config.cacheTimeout);
    
    this.responseCache.set(key, {
      response,
      timestamp: new Date(),
      expiresAt
    });
  }

  /**
   * Setup cache cleanup
   */
  private setupCacheCleanup(): void {
    setInterval(() => {
      const now = new Date();
      for (const [key, entry] of Array.from(this.responseCache.entries())) {
        if (entry.expiresAt < now) {
          this.responseCache.delete(key);
        }
      }
    }, 60000); // Clean every minute
  }

  /**
   * Generate unique ID
   */
  private generateId(): string {
    return Math.random().toString(36).substring(2) + Date.now().toString(36);
  }

  /**
   * Ensure service is initialized
   */
  private ensureInitialized(): void {
    if (!this.isInitialized) {
      throw new Error('AI Service not initialized');
    }
  }
}

// Global AI service instance
let globalAIService: AIService | null = null;

/**
 * Get the global AI service instance
 */
export const getAIService = (): AIService => {
  if (!globalAIService) {
    globalAIService = new AIService();
  }
  return globalAIService;
};

/**
 * Initialize the global AI service instance
 */
export const initializeAIService = async (config?: Partial<AIServiceConfig>): Promise<AIService> => {
  if (globalAIService) {
    globalAIService.configure(config || {});
  } else {
    globalAIService = new AIService(config);
  }

  await globalAIService.initialize();
  return globalAIService;
};

/**
 * Initialize the global AI service
 */
export const initializeAIService = async (config?: Partial<AIServiceConfig>): Promise<AIService> => {
  globalAIService = new AIService(config);
  await globalAIService.initialize();
  return globalAIService;
};
