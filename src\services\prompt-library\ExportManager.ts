/**
 * Export Manager for Prompt Library
 * Handles exporting prompts in various formats
 */

import {
  ExportOptions,
  Prompt,
  PromptLibraryError
} from './types';
import { getStorageManager } from '../../storage';
import { writeFile } from '../../core/filesystem';
import { LoggerFactory } from '../../core/logger';

const logger = LoggerFactory.getInstance().getLogger('ExportManager');

/**
 * Export Manager implementation
 */
export class ExportManager {
  /**
   * Export prompts based on options
   */
  async exportPrompts(options: ExportOptions): Promise<string> {
    try {
      const storage = getStorageManager();
      const promptRepository = storage.getRepository('prompts');

      // Get prompts based on filters
      let prompts = await promptRepository.findAll();
      prompts = this.applyExportFilters(prompts, options);

      // Format based on requested format
      switch (options.format) {
        case 'json':
          return this.formatAsJSON(prompts, options);
        case 'csv':
          return this.formatAsCSV(prompts, options);
        case 'markdown':
          return this.formatAsMarkdown(prompts, options);
        default:
          throw new PromptLibraryError(`Unsupported export format: ${options.format}`);
      }
    } catch (error) {
      logger.error('Failed to export prompts', { error, options });
      throw new PromptLibraryError('Failed to export prompts', error);
    }
  }

  /**
   * Export prompts to file
   */
  async exportToFile(filePath: string, options: ExportOptions): Promise<void> {
    try {
      const content = await this.exportPrompts(options);
      await writeFile(filePath, content);
      
      logger.info('Prompts exported to file', { filePath, format: options.format });
    } catch (error) {
      logger.error('Failed to export prompts to file', { error, filePath, options });
      throw new PromptLibraryError('Failed to export prompts to file', error);
    }
  }

  /**
   * Export specific category
   */
  async exportCategory(categoryId: string, format: string): Promise<string> {
    const options: ExportOptions = {
      format: format as any,
      includeMetadata: true,
      includeVersions: false,
      categories: [categoryId]
    };

    return this.exportPrompts(options);
  }

  /**
   * Export favorites
   */
  async exportFavorites(format: string): Promise<string> {
    try {
      const storage = getStorageManager();
      const promptRepository = storage.getRepository('prompts');

      const allPrompts = await promptRepository.findAll();
      const favorites = allPrompts.filter(p => p.isFavorite);

      const options: ExportOptions = {
        format: format as any,
        includeMetadata: true,
        includeVersions: false
      };

      switch (format) {
        case 'json':
          return this.formatAsJSON(favorites, options);
        case 'csv':
          return this.formatAsCSV(favorites, options);
        case 'markdown':
          return this.formatAsMarkdown(favorites, options);
        default:
          throw new PromptLibraryError(`Unsupported export format: ${format}`);
      }
    } catch (error) {
      logger.error('Failed to export favorites', { error, format });
      throw new PromptLibraryError('Failed to export favorites', error);
    }
  }

  /**
   * Apply export filters to prompts
   */
  private applyExportFilters(prompts: Prompt[], options: ExportOptions): Prompt[] {
    let filtered = prompts;

    if (options.categories && options.categories.length > 0) {
      filtered = filtered.filter(p => options.categories!.includes(p.category));
    }

    if (options.tags && options.tags.length > 0) {
      filtered = filtered.filter(p => 
        options.tags!.some(tag => p.tags.includes(tag))
      );
    }

    if (options.dateRange) {
      filtered = filtered.filter(p => 
        p.metadata.createdAt >= options.dateRange!.start &&
        p.metadata.createdAt <= options.dateRange!.end
      );
    }

    return filtered;
  }

  /**
   * Format prompts as JSON
   */
  private formatAsJSON(prompts: Prompt[], options: ExportOptions): string {
    const exportData = {
      exportedAt: new Date().toISOString(),
      format: 'json',
      includeMetadata: options.includeMetadata,
      includeVersions: options.includeVersions,
      count: prompts.length,
      prompts: prompts.map(prompt => {
        const exportPrompt: any = {
          id: prompt.id,
          title: prompt.title,
          content: prompt.content,
          description: prompt.description,
          category: prompt.category,
          tags: prompt.tags,
          variables: prompt.variables,
          version: prompt.version,
          isTemplate: prompt.isTemplate,
          isFavorite: prompt.isFavorite
        };

        if (options.includeMetadata) {
          exportPrompt.metadata = prompt.metadata;
          exportPrompt.usage = prompt.usage;
        }

        return exportPrompt;
      })
    };

    return JSON.stringify(exportData, null, 2);
  }

  /**
   * Format prompts as CSV
   */
  private formatAsCSV(prompts: Prompt[], options: ExportOptions): string {
    const headers = [
      'ID',
      'Title',
      'Content',
      'Description',
      'Category',
      'Tags',
      'Is Template',
      'Is Favorite'
    ];

    if (options.includeMetadata) {
      headers.push(
        'Author',
        'Created At',
        'Updated At',
        'Source',
        'Language',
        'Usage Count'
      );
    }

    const csvLines = [headers.join(',')];

    for (const prompt of prompts) {
      const row = [
        this.escapeCsvValue(prompt.id),
        this.escapeCsvValue(prompt.title),
        this.escapeCsvValue(prompt.content),
        this.escapeCsvValue(prompt.description || ''),
        this.escapeCsvValue(prompt.category),
        this.escapeCsvValue(prompt.tags.join('; ')),
        prompt.isTemplate.toString(),
        prompt.isFavorite.toString()
      ];

      if (options.includeMetadata) {
        row.push(
          this.escapeCsvValue(prompt.metadata.author),
          prompt.metadata.createdAt.toISOString(),
          prompt.metadata.updatedAt.toISOString(),
          prompt.metadata.source,
          prompt.metadata.language,
          prompt.usage.count.toString()
        );
      }

      csvLines.push(row.join(','));
    }

    return csvLines.join('\n');
  }

  /**
   * Format prompts as Markdown
   */
  private formatAsMarkdown(prompts: Prompt[], options: ExportOptions): string {
    const lines = [
      '# Exported Prompts',
      '',
      `Exported on: ${new Date().toISOString()}`,
      `Total prompts: ${prompts.length}`,
      ''
    ];

    for (const prompt of prompts) {
      lines.push(`## ${prompt.title}`);
      lines.push('');

      if (prompt.description) {
        lines.push(`**Description:** ${prompt.description}`);
        lines.push('');
      }

      lines.push(`**Category:** ${prompt.category}`);
      
      if (prompt.tags.length > 0) {
        lines.push(`**Tags:** ${prompt.tags.join(', ')}`);
      }

      if (prompt.isTemplate) {
        lines.push('**Type:** Template');
      }

      if (prompt.isFavorite) {
        lines.push('**Favorite:** Yes');
      }

      lines.push('');
      lines.push('**Content:**');
      lines.push('```');
      lines.push(prompt.content);
      lines.push('```');
      lines.push('');

      if (options.includeMetadata) {
        lines.push('**Metadata:**');
        lines.push(`- Author: ${prompt.metadata.author}`);
        lines.push(`- Created: ${prompt.metadata.createdAt.toISOString()}`);
        lines.push(`- Updated: ${prompt.metadata.updatedAt.toISOString()}`);
        lines.push(`- Usage: ${prompt.usage.count} times`);
        lines.push('');
      }

      lines.push('---');
      lines.push('');
    }

    return lines.join('\n');
  }

  /**
   * Escape CSV values
   */
  private escapeCsvValue(value: string): string {
    if (value.includes(',') || value.includes('"') || value.includes('\n')) {
      return `"${value.replace(/"/g, '""')}"`;
    }
    return value;
  }
}
