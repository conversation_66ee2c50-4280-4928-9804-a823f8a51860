/**
 * Animation Manager
 * Handles window animations and transitions
 */

import { AnimationConfig, WindowAnimation } from './types';
import { LoggerFactory } from '../../core/logger';

const logger = LoggerFactory.getInstance().getLogger('AnimationManager');

/**
 * Animation Manager implementation
 */
export class AnimationManager {
  private activeAnimations: Map<string, Animation> = new Map();

  /**
   * Animate element with specified animation
   */
  async animate(type: string, config: WindowAnimation, element?: HTMLElement): Promise<void> {
    try {
      const animationConfig = this.getAnimationConfig(type, config);
      
      if (!element) {
        // If no element provided, animate the document body (for global effects)
        element = document.body;
      }

      const animation = element.animate(
        animationConfig.keyframes,
        {
          duration: config.duration,
          easing: config.easing,
          fill: 'forwards'
        }
      );

      // Store animation reference
      const animationId = `${type}-${Date.now()}`;
      this.activeAnimations.set(animationId, animation);

      // Wait for animation to complete
      await animation.finished;

      // Clean up
      this.activeAnimations.delete(animationId);

      logger.debug('Animation completed', { type, duration: config.duration });
    } catch (error) {
      logger.error('Animation failed', { error, type });
    }
  }

  /**
   * Stop all active animations
   */
  stopAllAnimations(): void {
    for (const [id, animation] of this.activeAnimations) {
      animation.cancel();
      this.activeAnimations.delete(id);
    }
    
    logger.debug('All animations stopped');
  }

  /**
   * Stop specific animation
   */
  stopAnimation(animationId: string): void {
    const animation = this.activeAnimations.get(animationId);
    if (animation) {
      animation.cancel();
      this.activeAnimations.delete(animationId);
      logger.debug('Animation stopped', { animationId });
    }
  }

  /**
   * Get animation configuration for type
   */
  private getAnimationConfig(type: string, config: WindowAnimation): { keyframes: Keyframe[] } {
    switch (type) {
      case 'show':
        return this.getShowAnimation(config);
      case 'hide':
        return this.getHideAnimation(config);
      case 'slide-in':
        return this.getSlideInAnimation(config);
      case 'slide-out':
        return this.getSlideOutAnimation(config);
      case 'scale-in':
        return this.getScaleInAnimation(config);
      case 'scale-out':
        return this.getScaleOutAnimation(config);
      case 'bounce':
        return this.getBounceAnimation(config);
      default:
        return this.getFadeAnimation(config);
    }
  }

  /**
   * Get show animation keyframes
   */
  private getShowAnimation(config: WindowAnimation): { keyframes: Keyframe[] } {
    switch (config.type) {
      case 'slide':
        return {
          keyframes: [
            { transform: 'translateY(-20px)', opacity: 0 },
            { transform: 'translateY(0)', opacity: 1 }
          ]
        };
      case 'scale':
        return {
          keyframes: [
            { transform: 'scale(0.8)', opacity: 0 },
            { transform: 'scale(1)', opacity: 1 }
          ]
        };
      case 'fade':
      default:
        return {
          keyframes: [
            { opacity: 0 },
            { opacity: 1 }
          ]
        };
    }
  }

  /**
   * Get hide animation keyframes
   */
  private getHideAnimation(config: WindowAnimation): { keyframes: Keyframe[] } {
    switch (config.type) {
      case 'slide':
        return {
          keyframes: [
            { transform: 'translateY(0)', opacity: 1 },
            { transform: 'translateY(-20px)', opacity: 0 }
          ]
        };
      case 'scale':
        return {
          keyframes: [
            { transform: 'scale(1)', opacity: 1 },
            { transform: 'scale(0.8)', opacity: 0 }
          ]
        };
      case 'fade':
      default:
        return {
          keyframes: [
            { opacity: 1 },
            { opacity: 0 }
          ]
        };
    }
  }

  /**
   * Get slide-in animation keyframes
   */
  private getSlideInAnimation(config: WindowAnimation): { keyframes: Keyframe[] } {
    return {
      keyframes: [
        { transform: 'translateX(-100%)', opacity: 0 },
        { transform: 'translateX(0)', opacity: 1 }
      ]
    };
  }

  /**
   * Get slide-out animation keyframes
   */
  private getSlideOutAnimation(config: WindowAnimation): { keyframes: Keyframe[] } {
    return {
      keyframes: [
        { transform: 'translateX(0)', opacity: 1 },
        { transform: 'translateX(100%)', opacity: 0 }
      ]
    };
  }

  /**
   * Get scale-in animation keyframes
   */
  private getScaleInAnimation(config: WindowAnimation): { keyframes: Keyframe[] } {
    return {
      keyframes: [
        { transform: 'scale(0)', opacity: 0 },
        { transform: 'scale(1.1)', opacity: 0.8 },
        { transform: 'scale(1)', opacity: 1 }
      ]
    };
  }

  /**
   * Get scale-out animation keyframes
   */
  private getScaleOutAnimation(config: WindowAnimation): { keyframes: Keyframe[] } {
    return {
      keyframes: [
        { transform: 'scale(1)', opacity: 1 },
        { transform: 'scale(1.1)', opacity: 0.8 },
        { transform: 'scale(0)', opacity: 0 }
      ]
    };
  }

  /**
   * Get bounce animation keyframes
   */
  private getBounceAnimation(config: WindowAnimation): { keyframes: Keyframe[] } {
    return {
      keyframes: [
        { transform: 'translateY(0)', animationTimingFunction: 'ease-out' },
        { transform: 'translateY(-30px)', animationTimingFunction: 'ease-in' },
        { transform: 'translateY(0)', animationTimingFunction: 'ease-out' },
        { transform: 'translateY(-15px)', animationTimingFunction: 'ease-in' },
        { transform: 'translateY(0)', animationTimingFunction: 'ease-out' }
      ]
    };
  }

  /**
   * Get fade animation keyframes
   */
  private getFadeAnimation(config: WindowAnimation): { keyframes: Keyframe[] } {
    return {
      keyframes: [
        { opacity: 0 },
        { opacity: 1 }
      ]
    };
  }

  /**
   * Create custom animation
   */
  async createCustomAnimation(
    element: HTMLElement,
    keyframes: Keyframe[],
    options: KeyframeAnimationOptions
  ): Promise<void> {
    try {
      const animation = element.animate(keyframes, options);
      
      const animationId = `custom-${Date.now()}`;
      this.activeAnimations.set(animationId, animation);

      await animation.finished;
      this.activeAnimations.delete(animationId);

      logger.debug('Custom animation completed');
    } catch (error) {
      logger.error('Custom animation failed', { error });
    }
  }

  /**
   * Get active animation count
   */
  getActiveAnimationCount(): number {
    return this.activeAnimations.size;
  }

  /**
   * Check if animations are supported
   */
  isAnimationSupported(): boolean {
    return typeof Element !== 'undefined' && 'animate' in Element.prototype;
  }
}
