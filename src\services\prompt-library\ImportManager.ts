/**
 * Import Manager for Prompt Library
 * Handles importing prompts from various sources and formats
 */

import { v4 as uuidv4 } from 'uuid';
import {
  ImportOptions,
  ImportResult,
  ImportError,
  Prompt,
  PromptLibraryError
} from './types';
import { getStorageManager } from '../../storage';
import { readFile } from '../../core/filesystem';
import { LoggerFactory } from '../../core/logger';

const logger = LoggerFactory.getInstance().getLogger('ImportManager');

/**
 * Import Manager implementation
 */
export class ImportManager {
  /**
   * Import prompts from JSON data
   */
  async importFromJSON(jsonData: string, options: ImportOptions): Promise<ImportResult> {
    try {
      const data = JSON.parse(jsonData);
      return await this.processImportData(data, options);
    } catch (error) {
      logger.error('Failed to import from JSON', { error });
      throw new PromptLibraryError('Failed to parse JSON data', error);
    }
  }

  /**
   * Import prompts from file
   */
  async importFromFile(filePath: string, options: ImportOptions): Promise<ImportResult> {
    try {
      const fileContent = await readFile(filePath, { encoding: 'utf8' }) as string;
      
      if (filePath.endsWith('.json')) {
        return await this.importFromJSON(fileContent, options);
      } else {
        throw new PromptLibraryError(`Unsupported file format: ${filePath}`);
      }
    } catch (error) {
      logger.error('Failed to import from file', { error, filePath });
      throw new PromptLibraryError('Failed to import from file', error);
    }
  }

  /**
   * Import prompts from URL
   */
  async importFromURL(url: string, options: ImportOptions): Promise<ImportResult> {
    try {
      // This would typically use fetch or similar HTTP client
      // For now, we'll throw an error as it's not implemented
      throw new PromptLibraryError('URL import not yet implemented');
    } catch (error) {
      logger.error('Failed to import from URL', { error, url });
      throw new PromptLibraryError('Failed to import from URL', error);
    }
  }

  /**
   * Process import data
   */
  private async processImportData(data: any, options: ImportOptions): Promise<ImportResult> {
    const result: ImportResult = {
      imported: 0,
      skipped: 0,
      errors: [],
      warnings: []
    };

    try {
      const storage = getStorageManager();
      const promptRepository = storage.prompts;

      // Validate data structure
      if (!data.prompts || !Array.isArray(data.prompts)) {
        throw new PromptLibraryError('Invalid data format: missing prompts array');
      }

      for (let i = 0; i < data.prompts.length; i++) {
        const promptData = data.prompts[i];
        
        try {
          // Validate prompt data
          const validation = this.validatePromptData(promptData);
          if (!validation.isValid) {
            result.errors.push({
              line: i + 1,
              prompt: promptData,
              error: validation.error || 'Invalid prompt data'
            });
            continue;
          }

          // Check if prompt already exists
          const existingPrompt = options.preserveIds && promptData.id 
            ? await promptRepository.findById(promptData.id)
            : await this.findExistingPrompt(promptData);

          if (existingPrompt) {
            const resolvedPrompt = this.resolveConflicts(existingPrompt, promptData, options.mergeStrategy);
            
            if (resolvedPrompt) {
              await promptRepository.update(existingPrompt.id, resolvedPrompt);
              result.imported++;
            } else {
              result.skipped++;
            }
          } else {
            // Create new prompt
            const newPrompt = this.createPromptFromData(promptData, options);
            await promptRepository.save(newPrompt);
            result.imported++;
          }
        } catch (error) {
          result.errors.push({
            line: i + 1,
            prompt: promptData,
            error: error.message
          });
        }
      }

      logger.info('Import completed', { 
        imported: result.imported, 
        skipped: result.skipped, 
        errors: result.errors.length 
      });

      return result;
    } catch (error) {
      logger.error('Failed to process import data', { error });
      throw new PromptLibraryError('Failed to process import data', error);
    }
  }

  /**
   * Validate prompt data
   */
  private validatePromptData(data: any): { isValid: boolean; error?: string } {
    if (!data || typeof data !== 'object') {
      return { isValid: false, error: 'Prompt data must be an object' };
    }

    if (!data.title || typeof data.title !== 'string' || data.title.trim().length === 0) {
      return { isValid: false, error: 'Title is required and must be a non-empty string' };
    }

    if (!data.content || typeof data.content !== 'string' || data.content.trim().length === 0) {
      return { isValid: false, error: 'Content is required and must be a non-empty string' };
    }

    if (!data.category || typeof data.category !== 'string' || data.category.trim().length === 0) {
      return { isValid: false, error: 'Category is required and must be a non-empty string' };
    }

    if (data.tags && !Array.isArray(data.tags)) {
      return { isValid: false, error: 'Tags must be an array' };
    }

    if (data.variables && !Array.isArray(data.variables)) {
      return { isValid: false, error: 'Variables must be an array' };
    }

    return { isValid: true };
  }

  /**
   * Find existing prompt by title and content
   */
  private async findExistingPrompt(promptData: any): Promise<Prompt | null> {
    try {
      const storage = getStorageManager();
      const promptRepository = storage.prompts;

      const allPrompts = await promptRepository.findAll();
      
      // Look for exact title match first
      const titleMatch = allPrompts.find(p => 
        p.title.toLowerCase() === promptData.title.toLowerCase()
      );

      if (titleMatch) {
        return titleMatch;
      }

      // Look for content similarity
      const contentMatch = allPrompts.find(p => 
        this.calculateSimilarity(p.content, promptData.content) > 0.9
      );

      return contentMatch || null;
    } catch (error) {
      logger.error('Failed to find existing prompt', { error });
      return null;
    }
  }

  /**
   * Resolve conflicts between existing and imported prompts
   */
  private resolveConflicts(existing: Prompt, imported: any, strategy: string): Prompt | null {
    switch (strategy) {
      case 'skip':
        return null; // Don't import

      case 'overwrite':
        return {
          ...existing,
          title: imported.title,
          content: imported.content,
          description: imported.description,
          category: imported.category || existing.category,
          tags: imported.tags || existing.tags,
          variables: imported.variables || existing.variables,
          isTemplate: imported.isTemplate !== undefined ? imported.isTemplate : existing.isTemplate,
          isFavorite: imported.isFavorite !== undefined ? imported.isFavorite : existing.isFavorite,
          metadata: {
            ...existing.metadata,
            updatedAt: new Date()
          }
        };

      case 'create_new':
        // This will be handled by the caller as a new prompt
        return null;

      default:
        return null;
    }
  }

  /**
   * Create prompt from import data
   */
  private createPromptFromData(data: any, options: ImportOptions): Prompt {
    const now = new Date();
    
    return {
      id: (options.preserveIds && data.id) ? data.id : uuidv4(),
      title: data.title,
      content: data.content,
      description: data.description || '',
      category: data.category || options.defaultCategory || 'general',
      tags: data.tags || [],
      variables: data.variables || [],
      metadata: {
        author: data.metadata?.author || 'imported',
        createdAt: data.metadata?.createdAt ? new Date(data.metadata.createdAt) : now,
        updatedAt: now,
        lastUsedAt: data.metadata?.lastUsedAt ? new Date(data.metadata.lastUsedAt) : undefined,
        source: 'imported',
        language: data.metadata?.language || 'en',
        estimatedTokens: data.metadata?.estimatedTokens || this.estimateTokens(data.content)
      },
      version: data.version || 1,
      parentId: data.parentId,
      isTemplate: data.isTemplate || false,
      isFavorite: data.isFavorite || false,
      usage: {
        count: data.usage?.count || 0,
        lastUsed: data.usage?.lastUsed ? new Date(data.usage.lastUsed) : now,
        averageRating: data.usage?.averageRating,
        successRate: data.usage?.successRate
      }
    };
  }

  /**
   * Calculate similarity between two strings
   */
  private calculateSimilarity(str1: string, str2: string): number {
    const longer = str1.length > str2.length ? str1 : str2;
    const shorter = str1.length > str2.length ? str2 : str1;
    
    if (longer.length === 0) {
      return 1.0;
    }
    
    const distance = this.levenshteinDistance(longer, shorter);
    return (longer.length - distance) / longer.length;
  }

  /**
   * Calculate Levenshtein distance
   */
  private levenshteinDistance(str1: string, str2: string): number {
    const matrix = Array(str2.length + 1).fill(null).map(() => Array(str1.length + 1).fill(null));

    for (let i = 0; i <= str1.length; i++) {
      matrix[0][i] = i;
    }

    for (let j = 0; j <= str2.length; j++) {
      matrix[j][0] = j;
    }

    for (let j = 1; j <= str2.length; j++) {
      for (let i = 1; i <= str1.length; i++) {
        const indicator = str1[i - 1] === str2[j - 1] ? 0 : 1;
        matrix[j][i] = Math.min(
          matrix[j][i - 1] + 1,
          matrix[j - 1][i] + 1,
          matrix[j - 1][i - 1] + indicator
        );
      }
    }

    return matrix[str2.length][str1.length];
  }

  /**
   * Estimate token count for content
   */
  private estimateTokens(content: string): number {
    // Simple estimation: roughly 4 characters per token
    return Math.ceil(content.length / 4);
  }
}
