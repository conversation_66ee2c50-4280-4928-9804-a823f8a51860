/**
 * Configuration module exports
 * Centralized configuration management for PromptPilot Desktop
 */

// Core classes
export { ConfigurationManager } from './ConfigurationManager';
export { FileConfigurationStore } from './ConfigurationStore';

// Schema and defaults
export { DEFAULT_CONFIG_SCHEMA, getDefaultConfig, getSettingByPath } from './ConfigurationSchema';

// Validators
export {
  DefaultConfigValidator,
  HotkeyValidator,
  PathValidator,
  APIKeyValidator,
  ValidatorFactory
} from './validators';

// Types and interfaces
export * from './types';

// Convenience functions
let globalConfigManager: ConfigurationManager | null = null;

export const getConfigManager = (): ConfigurationManager => {
  if (!globalConfigManager) {
    globalConfigManager = new ConfigurationManager();
  }
  return globalConfigManager;
};

export const initializeConfig = async (options?: any): Promise<ConfigurationManager> => {
  globalConfigManager = new ConfigurationManager(options);
  await globalConfigManager.load();
  return globalConfigManager;
};

export const getConfig = <T>(key: string): T => {
  return getConfigManager().get<T>(key);
};

export const setConfig = async <T>(key: string, value: T): Promise<void> => {
  return getConfigManager().set(key, value);
};

export const resetConfig = async (key?: string): Promise<void> => {
  return getConfigManager().reset(key);
};
