/**
 * Floating Window module exports
 * Floating window management and UI capabilities
 */

// Core classes
export { FloatingWindow } from './FloatingWindow';
export { WindowManager, getWindowManager, initializeWindowManager } from './WindowManager';
export { WindowRenderer } from './WindowRenderer';
export { ThemeManager } from './ThemeManager';
export { AnimationManager } from './AnimationManager';

// Types and interfaces
export * from './types';

// Re-export commonly used types for convenience
export type {
  IFloatingWindow,
  IWindowManager,
  FloatingWindowConfig,
  WindowShowOptions,
  WindowContent,
  WindowState,
  WindowPosition,
  WindowSize,
  WindowTheme,
  UIComponent,
  WindowAnimation
} from './types';

// Convenience functions
export const createFloatingWindow = async (config?: any): Promise<any> => {
  return getWindowManager().createWindow(config);
};

export const getDefaultWindowManager = (): any => {
  return getWindowManager();
};

// Window management functions
export const showWindow = async (id: string, options?: any): Promise<void> => {
  return getWindowManager().showWindow(id, options);
};

export const hideWindow = async (id: string): Promise<void> => {
  return getWindowManager().hideWindow(id);
};

export const closeWindow = async (id: string): Promise<void> => {
  return getWindowManager().closeWindow(id);
};

export const focusWindow = async (id: string): Promise<void> => {
  return getWindowManager().focusWindow(id);
};

export const getAllWindows = (): any[] => {
  return getWindowManager().getAllWindows();
};

export const getVisibleWindows = (): any[] => {
  return getWindowManager().getVisibleWindows();
};

export const hideAllWindows = async (): Promise<void> => {
  return getWindowManager().hideAllWindows();
};

export const showAllWindows = async (): Promise<void> => {
  return getWindowManager().showAllWindows();
};

export const closeAllWindows = async (): Promise<void> => {
  return getWindowManager().closeAllWindows();
};

// Configuration functions
export const updateDefaultWindowConfig = (config: any): void => {
  return getWindowManager().updateDefaultConfig(config);
};

export const getDefaultWindowConfig = (): any => {
  return getWindowManager().getDefaultConfig();
};

// Built-in configurations
export const getDefaultFloatingWindowConfig = () => ({
  window: {
    width: 400,
    height: 300,
    minWidth: 200,
    minHeight: 150,
    alwaysOnTop: true,
    resizable: true,
    movable: true,
    minimizable: true,
    maximizable: true,
    closable: true,
    skipTaskbar: true,
    transparent: false,
    opacity: 1.0
  },
  ui: {
    theme: 'light' as const,
    accentColor: '#007bff',
    fontSize: 14,
    fontFamily: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif',
    borderRadius: 8,
    showTitleBar: true,
    showControls: true
  },
  behavior: {
    autoHide: false,
    autoHideDelay: 3000,
    stayOnTop: true,
    followCursor: false,
    snapToEdges: false,
    fadeInOut: true,
    rememberPosition: true,
    rememberSize: true
  },
  hotkeys: {
    show: 'Ctrl+Shift+P',
    hide: 'Escape',
    toggle: 'Ctrl+Shift+P',
    focus: 'Ctrl+Shift+F'
  }
});

// Window content helpers
export const createPromptInputContent = (prompt?: string): any => ({
  type: 'prompt-input',
  data: { prompt }
});

export const createPromptLibraryContent = (prompts?: any[]): any => ({
  type: 'prompt-library',
  data: { prompts }
});

export const createSettingsContent = (settings?: any): any => ({
  type: 'settings',
  data: settings
});

export const createHelpContent = (): any => ({
  type: 'help'
});

export const createCustomContent = (template: string, styles?: Record<string, string>): any => ({
  type: 'custom',
  template,
  styles
});

// Position and size helpers
export const createWindowPosition = (x: number, y: number): any => ({ x, y });

export const createWindowSize = (width: number, height: number): any => ({ width, height });

export const centerWindow = (windowWidth: number, windowHeight: number): any => {
  // In a real implementation, this would get actual screen dimensions
  const screenWidth = 1920;
  const screenHeight = 1080;
  
  return {
    x: Math.floor((screenWidth - windowWidth) / 2),
    y: Math.floor((screenHeight - windowHeight) / 2)
  };
};

// Animation helpers
export const createFadeAnimation = (duration: number = 200): any => ({
  type: 'fade',
  duration,
  easing: 'ease-in-out'
});

export const createSlideAnimation = (duration: number = 300): any => ({
  type: 'slide',
  duration,
  easing: 'ease-out'
});

export const createScaleAnimation = (duration: number = 250): any => ({
  type: 'scale',
  duration,
  easing: 'ease-in-out'
});

// Theme helpers
export const getAvailableThemes = (): string[] => {
  return ['light', 'dark', 'auto'];
};

export const createLightTheme = (): any => ({
  name: 'Light',
  colors: {
    primary: '#007bff',
    secondary: '#6c757d',
    background: '#ffffff',
    surface: '#f8f9fa',
    text: '#212529',
    textSecondary: '#6c757d',
    border: '#dee2e6',
    shadow: 'rgba(0, 0, 0, 0.1)',
    accent: '#007bff',
    success: '#28a745',
    warning: '#ffc107',
    error: '#dc3545'
  }
});

export const createDarkTheme = (): any => ({
  name: 'Dark',
  colors: {
    primary: '#0d6efd',
    secondary: '#6c757d',
    background: '#1a1a1a',
    surface: '#2d2d2d',
    text: '#ffffff',
    textSecondary: '#adb5bd',
    border: '#495057',
    shadow: 'rgba(0, 0, 0, 0.3)',
    accent: '#0d6efd',
    success: '#198754',
    warning: '#fd7e14',
    error: '#dc3545'
  }
});

// UI component helpers
export const createButton = (text: string, onClick?: () => void): any => ({
  id: `button-${Date.now()}`,
  type: 'button',
  props: { text },
  events: { onClick }
});

export const createTextInput = (placeholder?: string, onChange?: (value: string) => void): any => ({
  id: `input-${Date.now()}`,
  type: 'text-input',
  props: { placeholder },
  events: { onChange }
});

export const createTextArea = (placeholder?: string, onChange?: (value: string) => void): any => ({
  id: `textarea-${Date.now()}`,
  type: 'text-area',
  props: { placeholder },
  events: { onChange }
});

export const createDropdown = (options: string[], onChange?: (value: string) => void): any => ({
  id: `dropdown-${Date.now()}`,
  type: 'dropdown',
  props: { options },
  events: { onChange }
});

// Window state helpers
export const isWindowVisible = (window: any): boolean => {
  return window && window.isVisible();
};

export const getWindowState = (window: any): any => {
  return window ? window.getState() : null;
};

// Error handling helpers
export const isFloatingWindowError = (error: any): boolean => {
  return error && (
    error.name === 'FloatingWindowError' ||
    error.name === 'WindowCreationError' ||
    error.name === 'WindowOperationError'
  );
};

export const getErrorMessage = (error: any): string => {
  if (isFloatingWindowError(error)) {
    return error.message;
  }
  return 'An unknown floating window error occurred';
};

// Quick window functions
export const showPromptWindow = async (prompt?: string): Promise<any> => {
  const window = await createFloatingWindow({
    window: { width: 500, height: 400 }
  });
  
  await window.setContent(createPromptInputContent(prompt));
  await window.show({ center: true });
  
  return window;
};

export const showPromptLibraryWindow = async (): Promise<any> => {
  const window = await createFloatingWindow({
    window: { width: 600, height: 500 }
  });
  
  await window.setContent(createPromptLibraryContent());
  await window.show({ center: true });
  
  return window;
};

export const showSettingsWindow = async (): Promise<any> => {
  const window = await createFloatingWindow({
    window: { width: 400, height: 350 }
  });
  
  await window.setContent(createSettingsContent());
  await window.show({ center: true });
  
  return window;
};

export const showHelpWindow = async (): Promise<any> => {
  const window = await createFloatingWindow({
    window: { width: 450, height: 400 }
  });
  
  await window.setContent(createHelpContent());
  await window.show({ center: true });
  
  return window;
};

// Window management constants
export const WINDOW_TYPES = {
  PROMPT_INPUT: 'prompt-input',
  PROMPT_LIBRARY: 'prompt-library',
  SETTINGS: 'settings',
  HELP: 'help',
  CUSTOM: 'custom'
} as const;

export const ANIMATION_TYPES = {
  FADE: 'fade',
  SLIDE: 'slide',
  SCALE: 'scale',
  NONE: 'none'
} as const;

export const THEME_NAMES = {
  LIGHT: 'light',
  DARK: 'dark',
  AUTO: 'auto'
} as const;
