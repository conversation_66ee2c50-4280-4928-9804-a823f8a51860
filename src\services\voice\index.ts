/**
 * Voice Module exports
 * Speech-to-text capabilities with real-time transcription and voice commands
 */

// Core classes
export { VoiceModule, getVoiceModule, initializeVoiceModule } from './VoiceModule';
export { AudioRecorder } from './AudioRecorder';
export { STTBridge } from './STTBridge';
export { AudioProcessor } from './AudioProcessor';
export { VoiceCommandProcessor } from './VoiceCommandProcessor';

// Types and interfaces
export * from './types';

// Re-export commonly used types for convenience
export type {
  IVoiceModule,
  ISTTService,
  IAudioRecorder,
  IAudioProcessor,
  IVoiceCommandProcessor,
  VoiceModuleConfig,
  RecordingOptions,
  RecordingState,
  STTOptions,
  TranscriptionResult,
  TranscriptionChunk,
  VoiceCommand,
  VoiceCommandResult,
  AudioProcessingOptions,
  STTServiceConfig
} from './types';

// Convenience functions
export const createVoiceModule = async (config?: any): Promise<any> => {
  return initializeVoiceModule(config);
};

export const getDefaultVoiceModule = (): any => {
  return getVoiceModule();
};

// Recording functions
export const startRecording = async (options?: any): Promise<void> => {
  return getVoiceModule().startRecording(options);
};

export const stopRecording = async (): Promise<string> => {
  return getVoiceModule().stopRecording();
};

export const isRecording = (): boolean => {
  return getVoiceModule().isRecording();
};

export const getRecordingState = (): any => {
  return getVoiceModule().getRecordingState();
};

// File processing functions
export const processAudioFile = async (filePath: string, options?: any): Promise<string> => {
  return getVoiceModule().processAudioFile(filePath, options);
};

// Continuous listening functions
export const startContinuousListening = async (): Promise<void> => {
  return getVoiceModule().startContinuousListening();
};

export const stopContinuousListening = async (): Promise<void> => {
  return getVoiceModule().stopContinuousListening();
};

export const isContinuousListening = (): boolean => {
  return getVoiceModule().isContinuousListening();
};

// Voice command functions
export const registerCommand = (command: any): void => {
  return getVoiceModule().registerCommand(command);
};

export const unregisterCommand = (commandName: string): void => {
  return getVoiceModule().unregisterCommand(commandName);
};

export const getRegisteredCommands = (): any[] => {
  return getVoiceModule().getRegisteredCommands();
};

// Configuration functions
export const updateVoiceConfig = (config: any): void => {
  return getVoiceModule().updateConfig(config);
};

export const getVoiceConfig = (): any => {
  return getVoiceModule().getConfig();
};

// Built-in commands
export const createBuiltInCommands = () => ({
  enhancePrompt: {
    name: 'enhance-prompt',
    trigger: 'enhance this',
    description: 'Enhance the current prompt',
    execute: async (text: string) => {
      const promptText = text.replace(/^enhance this\s*/i, '');
      return { action: 'enhance', text: promptText };
    }
  },
  savePrompt: {
    name: 'save-prompt',
    trigger: 'save prompt',
    description: 'Save the current prompt',
    execute: async (text: string) => {
      const promptText = text.replace(/^save prompt\s*/i, '');
      return { action: 'save', text: promptText };
    }
  },
  captureScreen: {
    name: 'capture-screen',
    trigger: 'capture screen',
    description: 'Capture the current screen',
    execute: async () => {
      return { action: 'capture', type: 'screen' };
    }
  },
  getClipboard: {
    name: 'get-clipboard',
    trigger: 'get clipboard',
    description: 'Get clipboard content',
    execute: async () => {
      return { action: 'clipboard', type: 'get' };
    }
  },
  startRecording: {
    name: 'start-recording',
    trigger: 'start recording',
    description: 'Start voice recording',
    execute: async () => {
      await startRecording();
      return { action: 'recording', state: 'started' };
    }
  },
  stopRecording: {
    name: 'stop-recording',
    trigger: 'stop recording',
    description: 'Stop voice recording',
    execute: async () => {
      const text = await stopRecording();
      return { action: 'recording', state: 'stopped', text };
    }
  },
  clearText: {
    name: 'clear-text',
    trigger: 'clear text',
    description: 'Clear the current text',
    execute: async () => {
      return { action: 'clear', type: 'text' };
    }
  },
  repeatLast: {
    name: 'repeat-last',
    trigger: 'repeat last',
    description: 'Repeat the last action',
    execute: async () => {
      return { action: 'repeat', type: 'last' };
    }
  }
});

// Utility functions
export const validateVoiceCommand = (command: any): boolean => {
  try {
    if (!command.name || command.name.trim().length === 0) {
      return false;
    }
    if (!command.trigger || command.trigger.trim().length === 0) {
      return false;
    }
    if (!command.execute || typeof command.execute !== 'function') {
      return false;
    }
    return true;
  } catch {
    return false;
  }
};

export const normalizeAudioFormat = (format: string): 'wav' | 'mp3' | 'ogg' => {
  const normalized = format.toLowerCase();
  if (['wav', 'mp3', 'ogg'].includes(normalized)) {
    return normalized as 'wav' | 'mp3' | 'ogg';
  }
  return 'wav'; // Default fallback
};

export const estimateAudioDuration = (audioBlob: Blob, sampleRate: number = 16000): number => {
  // Simple estimation based on file size
  // This is approximate and would be more accurate with actual audio analysis
  const bytesPerSecond = sampleRate * 2; // 16-bit mono
  return audioBlob.size / bytesPerSecond;
};

export const getSupportedAudioFormats = (): string[] => {
  const formats = ['audio/webm;codecs=opus', 'audio/webm', 'audio/mp4', 'audio/wav'];
  return formats.filter(format => MediaRecorder.isTypeSupported(format));
};

export const checkMicrophonePermission = async (): Promise<boolean> => {
  try {
    if (!navigator.mediaDevices || !navigator.mediaDevices.getUserMedia) {
      return false;
    }
    
    const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
    stream.getTracks().forEach(track => track.stop());
    return true;
  } catch {
    return false;
  }
};

// Configuration helpers
export const getDefaultVoiceConfig = () => ({
  stt: {
    provider: 'openai' as const,
    defaultModel: 'whisper-1',
    defaultLanguage: 'en-US'
  },
  recording: {
    defaultFormat: 'wav' as const,
    defaultSampleRate: 16000,
    defaultChannels: 1,
    maxDuration: 300,
    autoStop: true,
    noiseReduction: true
  },
  commands: {
    enabled: true,
    customCommands: []
  },
  processing: {
    enableNoiseReduction: true,
    enableVolumeNormalization: true,
    enableFormatConversion: false
  }
});

export const createDefaultRecordingOptions = () => ({
  language: 'en-US',
  maxDuration: 300,
  autoStop: true,
  noiseReduction: true,
  format: 'wav' as const,
  sampleRate: 16000,
  channels: 1,
  bitDepth: 16
});

export const createDefaultSTTOptions = () => ({
  language: 'en-US',
  model: 'whisper-1',
  punctuation: true,
  profanityFilter: false,
  speakerDiarization: false,
  wordTimestamps: false
});

// Language support
export const getSupportedLanguages = (): string[] => {
  return [
    'en', 'es', 'fr', 'de', 'it', 'pt', 'ru', 'ja', 'ko', 'zh',
    'ar', 'hi', 'tr', 'pl', 'nl', 'sv', 'da', 'no', 'fi', 'he',
    'th', 'vi', 'id', 'ms', 'tl', 'uk', 'bg', 'hr', 'cs', 'sk'
  ];
};

export const getLanguageName = (code: string): string => {
  const languages: Record<string, string> = {
    'en': 'English',
    'es': 'Spanish',
    'fr': 'French',
    'de': 'German',
    'it': 'Italian',
    'pt': 'Portuguese',
    'ru': 'Russian',
    'ja': 'Japanese',
    'ko': 'Korean',
    'zh': 'Chinese',
    'ar': 'Arabic',
    'hi': 'Hindi',
    'tr': 'Turkish',
    'pl': 'Polish',
    'nl': 'Dutch',
    'sv': 'Swedish',
    'da': 'Danish',
    'no': 'Norwegian',
    'fi': 'Finnish',
    'he': 'Hebrew',
    'th': 'Thai',
    'vi': 'Vietnamese',
    'id': 'Indonesian',
    'ms': 'Malay',
    'tl': 'Filipino',
    'uk': 'Ukrainian',
    'bg': 'Bulgarian',
    'hr': 'Croatian',
    'cs': 'Czech',
    'sk': 'Slovak'
  };
  
  return languages[code] || code.toUpperCase();
};

// Error handling helpers
export const isVoiceModuleError = (error: any): boolean => {
  return error && (
    error.name === 'VoiceModuleError' ||
    error.name === 'AudioRecordingError' ||
    error.name === 'STTServiceError' ||
    error.name === 'VoiceCommandError'
  );
};

export const getErrorMessage = (error: any): string => {
  if (isVoiceModuleError(error)) {
    return error.message;
  }
  return 'An unknown voice module error occurred';
};
