/**
 * IPC Event Emitter
 * Manages event broadcasting between main and renderer processes
 */

import { EventEmitter } from 'events';
import { BrowserWindow } from 'electron';
import { IPCEvent, IPCEventEmitter as IIPCEventEmitter, IPCMessageType } from './types';
import { logger } from '../../core/logger';

export class IPCEventEmitter extends EventEmitter implements IIPCEventEmitter {
  private subscribers: Map<string, Set<Function>>;
  private eventHistory: Map<string, IPCEvent[]>;
  private maxHistorySize: number;

  constructor(maxHistorySize: number = 100) {
    super();
    this.subscribers = new Map();
    this.eventHistory = new Map();
    this.maxHistorySize = maxHistorySize;
    
    this.setupEventHandling();
  }

  /**
   * Emit event to all listeners and renderer processes
   */
  emit(channel: string, data: any): boolean {
    const event: IPCEvent = {
      channel,
      data,
      timestamp: Date.now(),
      source: 'main'
    };

    // Store in history
    this.addToHistory(channel, event);

    // Emit to main process listeners
    const mainResult = super.emit(channel, data);

    // Send to all renderer processes
    this.broadcastToRenderers(channel, data);

    logger.debug('IPC event emitted', { channel, hasMainListeners: mainResult });

    return mainResult;
  }

  /**
   * Add event listener
   */
  on(channel: string, listener: Function): this {
    // Add to subscribers tracking
    if (!this.subscribers.has(channel)) {
      this.subscribers.set(channel, new Set());
    }
    this.subscribers.get(channel)!.add(listener);

    // Add to EventEmitter
    super.on(channel, listener);

    logger.debug('IPC event listener added', { channel });

    return this;
  }

  /**
   * Remove event listener
   */
  off(channel: string, listener: Function): this {
    // Remove from subscribers tracking
    const channelSubscribers = this.subscribers.get(channel);
    if (channelSubscribers) {
      channelSubscribers.delete(listener);
      if (channelSubscribers.size === 0) {
        this.subscribers.delete(channel);
      }
    }

    // Remove from EventEmitter
    super.off(channel, listener);

    logger.debug('IPC event listener removed', { channel });

    return this;
  }

  /**
   * Add one-time event listener
   */
  once(channel: string, listener: Function): this {
    const wrappedListener = (...args: any[]) => {
      // Remove from subscribers tracking
      const channelSubscribers = this.subscribers.get(channel);
      if (channelSubscribers) {
        channelSubscribers.delete(wrappedListener);
        if (channelSubscribers.size === 0) {
          this.subscribers.delete(channel);
        }
      }

      listener(...args);
    };

    // Add to subscribers tracking
    if (!this.subscribers.has(channel)) {
      this.subscribers.set(channel, new Set());
    }
    this.subscribers.get(channel)!.add(wrappedListener);

    // Add to EventEmitter
    super.once(channel, wrappedListener);

    logger.debug('IPC one-time event listener added', { channel });

    return this;
  }

  /**
   * Remove all listeners for a channel
   */
  removeAllListeners(channel?: string): this {
    if (channel) {
      this.subscribers.delete(channel);
      super.removeAllListeners(channel);
      logger.debug('All IPC event listeners removed for channel', { channel });
    } else {
      this.subscribers.clear();
      super.removeAllListeners();
      logger.debug('All IPC event listeners removed');
    }

    return this;
  }

  /**
   * Get event history for a channel
   */
  getEventHistory(channel: string): IPCEvent[] {
    return this.eventHistory.get(channel) || [];
  }

  /**
   * Get all active subscribers
   */
  getSubscribers(): Map<string, Set<Function>> {
    return new Map(this.subscribers);
  }

  /**
   * Get subscriber count for a channel
   */
  getSubscriberCount(channel: string): number {
    return this.subscribers.get(channel)?.size || 0;
  }

  /**
   * Clear event history
   */
  clearHistory(channel?: string): void {
    if (channel) {
      this.eventHistory.delete(channel);
    } else {
      this.eventHistory.clear();
    }

    logger.debug('IPC event history cleared', { channel: channel || 'all' });
  }

  /**
   * Broadcast to all renderer processes
   */
  private broadcastToRenderers(channel: string, data: any): void {
    const windows = BrowserWindow.getAllWindows();
    let sentCount = 0;

    windows.forEach(window => {
      if (!window.isDestroyed() && window.webContents) {
        try {
          window.webContents.send(channel, data);
          sentCount++;
        } catch (error) {
          logger.error('Failed to send IPC event to renderer', { 
            channel, 
            windowId: window.id,
            error: error instanceof Error ? error.message : String(error)
          });
        }
      }
    });

    logger.debug('IPC event broadcast to renderers', { channel, sentCount, totalWindows: windows.length });
  }

  /**
   * Add event to history
   */
  private addToHistory(channel: string, event: IPCEvent): void {
    if (!this.eventHistory.has(channel)) {
      this.eventHistory.set(channel, []);
    }

    const history = this.eventHistory.get(channel)!;
    history.push(event);

    // Limit history size
    if (history.length > this.maxHistorySize) {
      history.shift();
    }
  }

  /**
   * Setup event handling
   */
  private setupEventHandling(): void {
    // Handle errors in event emission
    this.on('error', (error) => {
      logger.error('IPC EventEmitter error', { error });
    });

    // Log when max listeners warning is triggered
    this.on('newListener', (event, listener) => {
      const count = this.listenerCount(event);
      if (count > 10) { // Default max listeners warning threshold
        logger.warn('High number of IPC event listeners', { event, count });
      }
    });
  }
}

/**
 * Event Broadcaster
 * Provides convenient methods for broadcasting common events
 */
export class EventBroadcaster {
  private eventEmitter: IPCEventEmitter;

  constructor(eventEmitter: IPCEventEmitter) {
    this.eventEmitter = eventEmitter;
  }

  /**
   * Broadcast hotkey triggered event
   */
  broadcastHotkeyTriggered(hotkeyId: string): void {
    this.eventEmitter.emit(IPCMessageType.SYSTEM_HOTKEY_TRIGGERED, { hotkeyId });
  }

  /**
   * Broadcast voice transcript event
   */
  broadcastVoiceTranscript(transcript: string): void {
    this.eventEmitter.emit(IPCMessageType.VOICE_TRANSCRIPT, { transcript });
  }

  /**
   * Broadcast voice error event
   */
  broadcastVoiceError(error: string): void {
    this.eventEmitter.emit(IPCMessageType.VOICE_ERROR, { error });
  }

  /**
   * Broadcast system notification event
   */
  broadcastSystemNotification(notification: any): void {
    this.eventEmitter.emit(IPCMessageType.SYSTEM_NOTIFICATION, notification);
  }

  /**
   * Broadcast prompt updated event
   */
  broadcastPromptUpdated(prompt: any): void {
    this.eventEmitter.emit(IPCMessageType.EVENT_PROMPT_UPDATED, prompt);
  }

  /**
   * Broadcast category updated event
   */
  broadcastCategoryUpdated(category: any): void {
    this.eventEmitter.emit(IPCMessageType.EVENT_CATEGORY_UPDATED, category);
  }

  /**
   * Broadcast settings updated event
   */
  broadcastSettingsUpdated(setting: any): void {
    this.eventEmitter.emit(IPCMessageType.EVENT_SETTINGS_UPDATED, setting);
  }

  /**
   * Broadcast user updated event
   */
  broadcastUserUpdated(user: any): void {
    this.eventEmitter.emit(IPCMessageType.EVENT_USER_UPDATED, user);
  }

  /**
   * Broadcast error occurred event
   */
  broadcastErrorOccurred(error: any): void {
    this.eventEmitter.emit(IPCMessageType.EVENT_ERROR_OCCURRED, error);
  }

  /**
   * Get event emitter instance
   */
  getEventEmitter(): IPCEventEmitter {
    return this.eventEmitter;
  }
}
