/**
 * Voice Command Processor implementation
 * Handles voice command recognition and execution
 */

import {
  <PERSON>oice<PERSON>ommandProcessor,
  VoiceCommand,
  VoiceCommandResult,
  VoiceCommandError
} from './types';
import { LoggerFactory } from '../../core/logger';

const logger = LoggerFactory.getInstance().getLogger('VoiceCommandProcessor');

/**
 * Voice Command Processor implementation
 */
export class VoiceCommandProcessor implements IVoiceCommandProcessor {
  private commands: Map<string, VoiceCommand> = new Map();
  private triggerPatterns: Map<string, RegExp> = new Map();

  /**
   * Register a voice command
   */
  registerCommand(command: VoiceCommand): void {
    try {
      // Validate command
      this.validateCommand(command);

      // Store command
      this.commands.set(command.name, command);

      // Create trigger pattern
      const pattern = this.createTriggerPattern(command.trigger);
      this.triggerPatterns.set(command.name, pattern);

      logger.info('Voice command registered', { 
        name: command.name, 
        trigger: command.trigger 
      });
    } catch (error) {
      logger.error('Failed to register voice command', { error, command: command.name });
      throw new VoiceCommandError('Failed to register voice command', error);
    }
  }

  /**
   * Unregister a voice command
   */
  unregisterCommand(commandName: string): void {
    try {
      if (!this.commands.has(commandName)) {
        throw new VoiceCommandError(`Command not found: ${commandName}`);
      }

      this.commands.delete(commandName);
      this.triggerPatterns.delete(commandName);

      logger.info('Voice command unregistered', { name: commandName });
    } catch (error) {
      logger.error('Failed to unregister voice command', { error, commandName });
      throw new VoiceCommandError('Failed to unregister voice command', error);
    }
  }

  /**
   * Process transcription text for voice commands
   */
  async processTranscription(text: string): Promise<VoiceCommandResult | null> {
    try {
      const normalizedText = this.normalizeText(text);
      
      // Check each registered command
      for (const [commandName, pattern] of this.triggerPatterns.entries()) {
        const match = normalizedText.match(pattern);
        
        if (match) {
          const command = this.commands.get(commandName);
          if (!command) {
            continue;
          }

          logger.info('Voice command detected', { 
            command: commandName, 
            trigger: command.trigger,
            text: text.substring(0, 100)
          });

          try {
            // Execute command
            const result = await command.execute(text);
            
            const commandResult: VoiceCommandResult = {
              command: commandName,
              success: true,
              result
            };

            logger.info('Voice command executed successfully', { 
              command: commandName,
              result: typeof result === 'object' ? JSON.stringify(result).substring(0, 100) : result
            });

            return commandResult;
          } catch (executionError) {
            logger.error('Voice command execution failed', { 
              error: executionError, 
              command: commandName 
            });

            return {
              command: commandName,
              success: false,
              error: executionError.message
            };
          }
        }
      }

      // No command matched
      return null;
    } catch (error) {
      logger.error('Failed to process transcription for commands', { error, text });
      throw new VoiceCommandError('Failed to process transcription for commands', error);
    }
  }

  /**
   * Get all registered commands
   */
  getRegisteredCommands(): VoiceCommand[] {
    return Array.from(this.commands.values());
  }

  /**
   * Get command by name
   */
  getCommand(commandName: string): VoiceCommand | undefined {
    return this.commands.get(commandName);
  }

  /**
   * Check if command exists
   */
  hasCommand(commandName: string): boolean {
    return this.commands.has(commandName);
  }

  /**
   * Get command suggestions based on partial text
   */
  getCommandSuggestions(partialText: string): VoiceCommand[] {
    const normalizedText = this.normalizeText(partialText);
    const suggestions: VoiceCommand[] = [];

    for (const command of this.commands.values()) {
      const normalizedTrigger = this.normalizeText(command.trigger);
      
      if (normalizedTrigger.includes(normalizedText) || 
          normalizedText.includes(normalizedTrigger)) {
        suggestions.push(command);
      }
    }

    return suggestions.sort((a, b) => a.trigger.length - b.trigger.length);
  }

  /**
   * Validate command structure
   */
  private validateCommand(command: VoiceCommand): void {
    if (!command.name || command.name.trim().length === 0) {
      throw new VoiceCommandError('Command name is required');
    }

    if (!command.trigger || command.trigger.trim().length === 0) {
      throw new VoiceCommandError('Command trigger is required');
    }

    if (!command.execute || typeof command.execute !== 'function') {
      throw new VoiceCommandError('Command execute function is required');
    }

    // Check for duplicate names
    if (this.commands.has(command.name)) {
      throw new VoiceCommandError(`Command with name '${command.name}' already exists`);
    }

    // Check for conflicting triggers
    const normalizedTrigger = this.normalizeText(command.trigger);
    for (const existingCommand of this.commands.values()) {
      const existingTrigger = this.normalizeText(existingCommand.trigger);
      
      if (normalizedTrigger === existingTrigger) {
        throw new VoiceCommandError(`Command trigger '${command.trigger}' conflicts with existing command '${existingCommand.name}'`);
      }
    }
  }

  /**
   * Create regex pattern for trigger matching
   */
  private createTriggerPattern(trigger: string): RegExp {
    // Escape special regex characters
    const escapedTrigger = trigger.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
    
    // Create pattern that matches the trigger at word boundaries
    // Allow for some flexibility with spacing and punctuation
    const pattern = `\\b${escapedTrigger.replace(/\s+/g, '\\s+')}\\b`;
    
    return new RegExp(pattern, 'i'); // Case insensitive
  }

  /**
   * Normalize text for consistent matching
   */
  private normalizeText(text: string): string {
    return text
      .toLowerCase()
      .trim()
      .replace(/[^\w\s]/g, '') // Remove punctuation
      .replace(/\s+/g, ' '); // Normalize whitespace
  }

  /**
   * Clear all commands
   */
  clearAllCommands(): void {
    this.commands.clear();
    this.triggerPatterns.clear();
    logger.info('All voice commands cleared');
  }

  /**
   * Get command statistics
   */
  getCommandStats(): { total: number; triggers: string[] } {
    return {
      total: this.commands.size,
      triggers: Array.from(this.commands.values()).map(cmd => cmd.trigger)
    };
  }
}
