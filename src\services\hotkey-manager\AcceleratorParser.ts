/**
 * Accelerator Parser
 * Parses and validates hotkey accelerator strings
 */

import { ParsedAccelerator } from './types';
import { LoggerFactory } from '../../core/logger';

const logger = LoggerFactory.getInstance().getLogger('AcceleratorParser');

/**
 * Accelerator Parser implementation
 */
export class AcceleratorParser {
  private readonly validModifiers = new Set(['Ctrl', 'Alt', 'Shift', 'Cmd', 'Win', 'Super', 'Meta']);
  private readonly validKeys = new Set([
    // Letters
    'A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J', 'K', 'L', 'M',
    'N', 'O', 'P', 'Q', 'R', 'S', 'T', 'U', 'V', 'W', 'X', 'Y', 'Z',
    // Numbers
    '0', '1', '2', '3', '4', '5', '6', '7', '8', '9',
    // Function keys
    'F1', 'F2', 'F3', 'F4', 'F5', 'F6', 'F7', 'F8', 'F9', 'F10', 'F11', 'F12',
    // Special keys
    'Space', 'Enter', 'Return', 'Escape', 'Tab', 'Backspace', 'BackSpace', 'Delete',
    'Insert', 'Home', 'End', 'PageUp', 'PageDown', 'Up', 'Down', 'Left', 'Right',
    // Punctuation
    'Plus', 'Minus', 'Equal', 'Comma', 'Period', 'Slash', 'Backslash',
    'Semicolon', 'Quote', 'Backquote', 'BracketLeft', 'BracketRight'
  ]);

  /**
   * Parse accelerator string
   */
  parse(accelerator: string): ParsedAccelerator {
    try {
      if (!accelerator || typeof accelerator !== 'string') {
        return {
          modifiers: [],
          key: '',
          isValid: false,
          normalized: ''
        };
      }

      const parts = accelerator.split('+').map(part => part.trim());
      
      if (parts.length === 0) {
        return {
          modifiers: [],
          key: '',
          isValid: false,
          normalized: ''
        };
      }

      const modifiers: string[] = [];
      let key = '';

      // Process each part
      for (let i = 0; i < parts.length; i++) {
        const part = parts[i];
        const normalizedPart = this.normalizeKeyName(part);

        if (i === parts.length - 1) {
          // Last part should be the key
          key = normalizedPart;
        } else {
          // Earlier parts should be modifiers
          if (this.isValidModifier(normalizedPart)) {
            modifiers.push(normalizedPart);
          } else {
            logger.warn('Invalid modifier in accelerator', { part: normalizedPart, accelerator });
            return {
              modifiers: [],
              key: '',
              isValid: false,
              normalized: ''
            };
          }
        }
      }

      // Validate key
      if (!this.isValidKey(key)) {
        logger.warn('Invalid key in accelerator', { key, accelerator });
        return {
          modifiers: [],
          key: '',
          isValid: false,
          normalized: ''
        };
      }

      // Remove duplicates and sort modifiers
      const uniqueModifiers = [...new Set(modifiers)].sort();
      const normalized = this.buildNormalizedAccelerator(uniqueModifiers, key);

      return {
        modifiers: uniqueModifiers,
        key,
        isValid: true,
        normalized
      };
    } catch (error) {
      logger.error('Failed to parse accelerator', { error, accelerator });
      return {
        modifiers: [],
        key: '',
        isValid: false,
        normalized: ''
      };
    }
  }

  /**
   * Normalize accelerator string
   */
  normalize(accelerator: string): string {
    const parsed = this.parse(accelerator);
    return parsed.isValid ? parsed.normalized : accelerator;
  }

  /**
   * Validate accelerator string
   */
  validate(accelerator: string): boolean {
    return this.parse(accelerator).isValid;
  }

  /**
   * Check if string is a valid modifier
   */
  private isValidModifier(modifier: string): boolean {
    return this.validModifiers.has(modifier);
  }

  /**
   * Check if string is a valid key
   */
  private isValidKey(key: string): boolean {
    return this.validKeys.has(key);
  }

  /**
   * Normalize key name
   */
  private normalizeKeyName(keyName: string): string {
    // Handle common aliases and variations
    const aliases: Record<string, string> = {
      // Modifier aliases
      'control': 'Ctrl',
      'ctrl': 'Ctrl',
      'command': 'Cmd',
      'cmd': 'Cmd',
      'meta': process.platform === 'darwin' ? 'Cmd' : 'Win',
      'option': 'Alt',
      'alt': 'Alt',
      'shift': 'Shift',
      'win': 'Win',
      'windows': 'Win',
      'super': 'Super',
      
      // Key aliases
      'esc': 'Escape',
      'escape': 'Escape',
      'enter': 'Enter',
      'return': 'Return',
      'space': 'Space',
      'tab': 'Tab',
      'backspace': 'Backspace',
      'delete': 'Delete',
      'del': 'Delete',
      'insert': 'Insert',
      'ins': 'Insert',
      'home': 'Home',
      'end': 'End',
      'pageup': 'PageUp',
      'pagedown': 'PageDown',
      'up': 'Up',
      'down': 'Down',
      'left': 'Left',
      'right': 'Right',
      
      // Punctuation aliases
      '+': 'Plus',
      '-': 'Minus',
      '=': 'Equal',
      ',': 'Comma',
      '.': 'Period',
      '/': 'Slash',
      '\\': 'Backslash',
      ';': 'Semicolon',
      "'": 'Quote',
      '`': 'Backquote',
      '[': 'BracketLeft',
      ']': 'BracketRight'
    };

    const lowerKey = keyName.toLowerCase();
    if (aliases[lowerKey]) {
      return aliases[lowerKey];
    }

    // Handle single letters and numbers
    if (keyName.length === 1) {
      const char = keyName.toUpperCase();
      if (/[A-Z0-9]/.test(char)) {
        return char;
      }
    }

    // Handle function keys
    if (/^f\d{1,2}$/i.test(keyName)) {
      return keyName.toUpperCase();
    }

    // Default: capitalize first letter
    return keyName.charAt(0).toUpperCase() + keyName.slice(1).toLowerCase();
  }

  /**
   * Build normalized accelerator string
   */
  private buildNormalizedAccelerator(modifiers: string[], key: string): string {
    // Define modifier order for consistency
    const modifierOrder = ['Ctrl', 'Alt', 'Shift', 'Cmd', 'Win', 'Super', 'Meta'];
    
    const sortedModifiers = modifiers.sort((a, b) => {
      const indexA = modifierOrder.indexOf(a);
      const indexB = modifierOrder.indexOf(b);
      
      if (indexA === -1 && indexB === -1) return a.localeCompare(b);
      if (indexA === -1) return 1;
      if (indexB === -1) return -1;
      
      return indexA - indexB;
    });

    return [...sortedModifiers, key].join('+');
  }

  /**
   * Get platform-specific modifier mapping
   */
  getPlatformModifiers(): Record<string, string> {
    switch (process.platform) {
      case 'darwin':
        return {
          'Ctrl': 'Ctrl',
          'Alt': 'Alt',
          'Shift': 'Shift',
          'Cmd': 'Cmd',
          'Meta': 'Cmd'
        };
      case 'win32':
        return {
          'Ctrl': 'Ctrl',
          'Alt': 'Alt',
          'Shift': 'Shift',
          'Win': 'Win',
          'Meta': 'Win'
        };
      case 'linux':
        return {
          'Ctrl': 'Ctrl',
          'Alt': 'Alt',
          'Shift': 'Shift',
          'Super': 'Super',
          'Meta': 'Super'
        };
      default:
        return {
          'Ctrl': 'Ctrl',
          'Alt': 'Alt',
          'Shift': 'Shift'
        };
    }
  }

  /**
   * Convert accelerator to platform-specific format
   */
  toPlatformFormat(accelerator: string): string {
    const parsed = this.parse(accelerator);
    if (!parsed.isValid) {
      return accelerator;
    }

    const platformModifiers = this.getPlatformModifiers();
    const convertedModifiers = parsed.modifiers.map(mod => platformModifiers[mod] || mod);
    
    return this.buildNormalizedAccelerator(convertedModifiers, parsed.key);
  }
}
