/**
 * Tag Manager for Prompt Library
 * Manages tags, suggestions, and tag-based organization
 */

import {
  Tag,
  PromptLibraryError
} from './types';
import { getStorageManager } from '../../storage';
import { LoggerFactory } from '../../core/logger';

const logger = LoggerFactory.getInstance().getLogger('TagManager');

/**
 * Tag Manager implementation
 */
export class TagManager {
  private tagCache: Map<string, Tag> = new Map();
  private lastCacheUpdate: Date = new Date(0);
  private cacheTimeout: number = 5 * 60 * 1000; // 5 minutes

  /**
   * Create a new tag
   */
  async createTag(name: string, options?: Partial<Tag>): Promise<void> {
    try {
      const normalizedName = this.normalizeTagName(name);
      
      if (!this.validateTagName(normalizedName)) {
        throw new PromptLibraryError(`Invalid tag name: ${name}`);
      }

      const storage = getStorageManager();
      const tagRepository = storage.system; // Using system repository for tags

      // Check if tag already exists
      const existing = await tagRepository.findOne({ name: normalizedName });
      if (existing) {
        return; // Tag already exists, no need to create
      }

      const newTag: Tag = {
        name: normalizedName,
        color: options?.color,
        description: options?.description,
        usage: 0,
        createdAt: new Date()
      };

      await tagRepository.save(newTag);
      this.tagCache.set(normalizedName, newTag);

      logger.info('Tag created', { name: normalizedName });
    } catch (error) {
      logger.error('Failed to create tag', { error, name });
      throw new PromptLibraryError('Failed to create tag', error);
    }
  }

  /**
   * Delete a tag
   */
  async deleteTag(name: string): Promise<void> {
    try {
      const normalizedName = this.normalizeTagName(name);
      const storage = getStorageManager();
      const tagRepository = storage.system; // Using system repository for tags
      const promptRepository = storage.prompts;

      // Remove tag from all prompts
      const allPrompts = await promptRepository.findAll();
      for (const prompt of allPrompts) {
        if (prompt.tags.includes(normalizedName)) {
          const updatedTags = prompt.tags.filter(tag => tag !== normalizedName);
          await promptRepository.update(prompt.id, { tags: updatedTags });
        }
      }

      // Delete the tag
      await tagRepository.deleteOne({ name: normalizedName });
      this.tagCache.delete(normalizedName);

      logger.info('Tag deleted', { name: normalizedName });
    } catch (error) {
      logger.error('Failed to delete tag', { error, name });
      throw new PromptLibraryError('Failed to delete tag', error);
    }
  }

  /**
   * Rename a tag
   */
  async renameTag(oldName: string, newName: string): Promise<void> {
    try {
      const oldNormalizedName = this.normalizeTagName(oldName);
      const newNormalizedName = this.normalizeTagName(newName);

      if (!this.validateTagName(newNormalizedName)) {
        throw new PromptLibraryError(`Invalid new tag name: ${newName}`);
      }

      const storage = getStorageManager();
      const tagRepository = storage.system; // Using system repository for tags
      const promptRepository = storage.prompts;

      // Check if old tag exists
      const oldTag = await tagRepository.findOne({ name: oldNormalizedName });
      if (!oldTag) {
        throw new PromptLibraryError(`Tag not found: ${oldName}`);
      }

      // Check if new tag name already exists
      const existingNewTag = await tagRepository.findOne({ name: newNormalizedName });
      if (existingNewTag) {
        throw new PromptLibraryError(`Tag already exists: ${newName}`);
      }

      // Update tag in database
      await tagRepository.update({ name: oldNormalizedName }, { name: newNormalizedName });

      // Update tag in all prompts
      const allPrompts = await promptRepository.findAll();
      for (const prompt of allPrompts) {
        if (prompt.tags.includes(oldNormalizedName)) {
          const updatedTags = prompt.tags.map(tag => 
            tag === oldNormalizedName ? newNormalizedName : tag
          );
          await promptRepository.update(prompt.id, { tags: updatedTags });
        }
      }

      // Update cache
      this.tagCache.delete(oldNormalizedName);
      this.tagCache.set(newNormalizedName, { ...oldTag, name: newNormalizedName });

      logger.info('Tag renamed', { oldName: oldNormalizedName, newName: newNormalizedName });
    } catch (error) {
      logger.error('Failed to rename tag', { error, oldName, newName });
      throw new PromptLibraryError('Failed to rename tag', error);
    }
  }

  /**
   * Get popular tags
   */
  async getPopularTags(limit: number = 20): Promise<Tag[]> {
    try {
      await this.updateTagUsage();
      await this.refreshCacheIfNeeded();

      const tags = Array.from(this.tagCache.values())
        .sort((a, b) => b.usage - a.usage)
        .slice(0, limit);

      return tags;
    } catch (error) {
      logger.error('Failed to get popular tags', { error, limit });
      throw new PromptLibraryError('Failed to get popular tags', error);
    }
  }

  /**
   * Get tag suggestions based on partial input
   */
  async getTagSuggestions(partial: string): Promise<string[]> {
    try {
      await this.refreshCacheIfNeeded();

      const normalizedPartial = this.normalizeTagName(partial);
      const suggestions: string[] = [];

      for (const tag of Array.from(this.tagCache.values())) {
        if (tag.name.startsWith(normalizedPartial)) {
          suggestions.push(tag.name);
        }
      }

      // Sort by usage (most used first) and then alphabetically
      suggestions.sort((a, b) => {
        const tagA = this.tagCache.get(a);
        const tagB = this.tagCache.get(b);
        
        if (tagA && tagB) {
          if (tagA.usage !== tagB.usage) {
            return tagB.usage - tagA.usage;
          }
        }
        
        return a.localeCompare(b);
      });

      return suggestions.slice(0, 10); // Limit to 10 suggestions
    } catch (error) {
      logger.error('Failed to get tag suggestions', { error, partial });
      throw new PromptLibraryError('Failed to get tag suggestions', error);
    }
  }

  /**
   * Update tag usage counts
   */
  async updateTagUsage(): Promise<void> {
    try {
      const storage = getStorageManager();
      const promptRepository = storage.prompts;
      const tagRepository = storage.system; // Using system repository for tags

      // Count tag usage from all prompts
      const allPrompts = await promptRepository.findAll();
      const tagUsage = new Map<string, number>();

      for (const prompt of allPrompts) {
        for (const tag of prompt.tags) {
          const normalizedTag = this.normalizeTagName(tag);
          tagUsage.set(normalizedTag, (tagUsage.get(normalizedTag) || 0) + 1);
        }
      }

      // Update tag usage in database
      for (const [tagName, usage] of Array.from(tagUsage.entries())) {
        const existingTag = await tagRepository.findOne({ name: tagName });
        
        if (existingTag) {
          await tagRepository.update({ name: tagName }, { usage });
          
          // Update cache
          if (this.tagCache.has(tagName)) {
            const cachedTag = this.tagCache.get(tagName)!;
            this.tagCache.set(tagName, { ...cachedTag, usage });
          }
        } else {
          // Create tag if it doesn't exist
          await this.createTag(tagName);
          await tagRepository.update({ name: tagName }, { usage });
        }
      }

      // Remove tags with zero usage
      const allTags = await tagRepository.findAll();
      for (const tag of allTags) {
        if (!tagUsage.has(tag.name)) {
          await tagRepository.deleteOne({ name: tag.name });
          this.tagCache.delete(tag.name);
        }
      }

      this.lastCacheUpdate = new Date();
      logger.debug('Tag usage updated', { uniqueTags: tagUsage.size });
    } catch (error) {
      logger.error('Failed to update tag usage', { error });
      throw new PromptLibraryError('Failed to update tag usage', error);
    }
  }

  /**
   * Get all tags
   */
  async getAllTags(): Promise<Tag[]> {
    try {
      await this.refreshCacheIfNeeded();
      return Array.from(this.tagCache.values());
    } catch (error) {
      logger.error('Failed to get all tags', { error });
      throw new PromptLibraryError('Failed to get all tags', error);
    }
  }

  /**
   * Validate tag name
   */
  validateTagName(name: string): boolean {
    // Tag name rules:
    // - Must be 1-50 characters
    // - Can contain letters, numbers, hyphens, underscores
    // - Cannot start or end with special characters
    // - Cannot contain spaces or other special characters
    
    if (!name || name.length === 0 || name.length > 50) {
      return false;
    }

    const validPattern = /^[a-z0-9][a-z0-9_-]*[a-z0-9]$|^[a-z0-9]$/;
    return validPattern.test(name);
  }

  /**
   * Normalize tag name
   */
  normalizeTagName(name: string): string {
    return name
      .toLowerCase()
      .trim()
      .replace(/\s+/g, '-') // Replace spaces with hyphens
      .replace(/[^a-z0-9_-]/g, '') // Remove invalid characters
      .replace(/^[-_]+|[-_]+$/g, '') // Remove leading/trailing special chars
      .replace(/[-_]{2,}/g, '-'); // Replace multiple consecutive special chars with single hyphen
  }

  /**
   * Refresh tag cache if needed
   */
  private async refreshCacheIfNeeded(): Promise<void> {
    const now = new Date();
    const timeSinceUpdate = now.getTime() - this.lastCacheUpdate.getTime();

    if (timeSinceUpdate > this.cacheTimeout || this.tagCache.size === 0) {
      await this.refreshCache();
    }
  }

  /**
   * Refresh tag cache from database
   */
  private async refreshCache(): Promise<void> {
    try {
      const storage = getStorageManager();
      const tagRepository = storage.system; // Using system repository for tags

      const allTags = await tagRepository.findAll();
      
      this.tagCache.clear();
      for (const tag of allTags) {
        this.tagCache.set(tag.name, tag);
      }

      this.lastCacheUpdate = new Date();
      logger.debug('Tag cache refreshed', { tagCount: allTags.length });
    } catch (error) {
      logger.error('Failed to refresh tag cache', { error });
      throw error;
    }
  }
}
