/**
 * File Content Reader
 * Reads and processes various file types
 */

import { IFileReader, FileReadResult, FileMetadata, FileReadError } from './types';
import { readFile, getFileStats } from '../../core/filesystem';
import { LoggerFactory } from '../../core/logger';
import * as path from 'path';

const logger = LoggerFactory.getInstance().getLogger('FileContentReader');

export interface FileConfig {
  maxFileSize: number;
  supportedExtensions: string[];
  encoding: string;
}

/**
 * File Content Reader implementation
 */
export class FileContentReader implements IFileReader {
  private config: FileConfig;

  constructor(config: FileConfig) {
    this.config = config;
  }

  /**
   * Read file content
   */
  async readFile(filePath: string): Promise<string> {
    try {
      // Check if file is supported
      if (!this.isSupported(filePath)) {
        throw new FileReadError(`Unsupported file type: ${path.extname(filePath)}`);
      }

      // Get file stats
      const stats = await getFileStats(filePath);
      
      // Check file size
      if (stats.size > this.config.maxFileSize) {
        throw new FileReadError(`File too large: ${stats.size} bytes (max: ${this.config.maxFileSize})`);
      }

      // Read file content
      const content = await readFile(filePath, this.config.encoding);
      
      // Process content based on file type
      const processedContent = this.processContent(content, path.extname(filePath));

      logger.info('File read successfully', { 
        filePath, 
        size: stats.size,
        contentLength: processedContent.length
      });

      return processedContent;
    } catch (error) {
      logger.error('Failed to read file', { error, filePath });
      throw new FileReadError('Failed to read file', error);
    }
  }

  /**
   * Read multiple files
   */
  async readMultipleFiles(filePaths: string[]): Promise<Record<string, string>> {
    const results: Record<string, string> = {};
    const errors: string[] = [];

    for (const filePath of filePaths) {
      try {
        results[filePath] = await this.readFile(filePath);
      } catch (error) {
        errors.push(`${filePath}: ${error.message}`);
        logger.warn('Failed to read file in batch', { error, filePath });
      }
    }

    if (errors.length > 0) {
      logger.warn('Some files failed to read', { errors, totalFiles: filePaths.length });
    }

    logger.info('Multiple files read', { 
      successful: Object.keys(results).length,
      failed: errors.length,
      total: filePaths.length
    });

    return results;
  }

  /**
   * Get supported file extensions
   */
  getSupportedExtensions(): string[] {
    return [...this.config.supportedExtensions];
  }

  /**
   * Check if file type is supported
   */
  isSupported(filePath: string): boolean {
    const extension = path.extname(filePath).toLowerCase();
    return this.config.supportedExtensions.includes(extension);
  }

  /**
   * Read file with metadata
   */
  async readFileWithMetadata(filePath: string): Promise<FileReadResult> {
    try {
      const content = await this.readFile(filePath);
      const stats = await getFileStats(filePath);
      
      const metadata: FileMetadata = {
        path: filePath,
        size: stats.size,
        extension: path.extname(filePath),
        mimeType: this.getMimeType(filePath),
        encoding: this.config.encoding,
        lastModified: stats.mtime
      };

      return { content, metadata };
    } catch (error) {
      logger.error('Failed to read file with metadata', { error, filePath });
      throw new FileReadError('Failed to read file with metadata', error);
    }
  }

  /**
   * Process content based on file type
   */
  private processContent(content: string, extension: string): string {
    switch (extension.toLowerCase()) {
      case '.json':
        return this.processJsonContent(content);
      case '.csv':
        return this.processCsvContent(content);
      case '.html':
      case '.htm':
        return this.processHtmlContent(content);
      case '.md':
      case '.markdown':
        return this.processMarkdownContent(content);
      case '.js':
      case '.ts':
      case '.py':
      case '.java':
      case '.cpp':
      case '.c':
        return this.processCodeContent(content);
      default:
        return this.processTextContent(content);
    }
  }

  /**
   * Process JSON content
   */
  private processJsonContent(content: string): string {
    try {
      const parsed = JSON.parse(content);
      return JSON.stringify(parsed, null, 2);
    } catch (error) {
      logger.warn('Invalid JSON content, returning as-is', { error });
      return content;
    }
  }

  /**
   * Process CSV content
   */
  private processCsvContent(content: string): string {
    // Simple CSV processing - convert to readable format
    const lines = content.split('\n');
    if (lines.length === 0) return content;

    const headers = lines[0].split(',').map(h => h.trim());
    const rows = lines.slice(1).map(line => line.split(',').map(cell => cell.trim()));

    let processed = `CSV Data (${rows.length} rows):\n\n`;
    processed += `Headers: ${headers.join(' | ')}\n`;
    processed += '-'.repeat(headers.join(' | ').length) + '\n';

    // Show first few rows
    const maxRows = Math.min(5, rows.length);
    for (let i = 0; i < maxRows; i++) {
      processed += `${rows[i].join(' | ')}\n`;
    }

    if (rows.length > maxRows) {
      processed += `... and ${rows.length - maxRows} more rows\n`;
    }

    return processed;
  }

  /**
   * Process HTML content
   */
  private processHtmlContent(content: string): string {
    // Simple HTML text extraction
    return content
      .replace(/<script[^>]*>[\s\S]*?<\/script>/gi, '')
      .replace(/<style[^>]*>[\s\S]*?<\/style>/gi, '')
      .replace(/<[^>]*>/g, '')
      .replace(/\s+/g, ' ')
      .trim();
  }

  /**
   * Process Markdown content
   */
  private processMarkdownContent(content: string): string {
    // Keep markdown formatting but clean up
    return content
      .replace(/\r\n/g, '\n')
      .replace(/\n{3,}/g, '\n\n')
      .trim();
  }

  /**
   * Process code content
   */
  private processCodeContent(content: string): string {
    // Add line numbers for better readability
    const lines = content.split('\n');
    const maxLineNumber = lines.length.toString().length;
    
    return lines
      .map((line, index) => {
        const lineNumber = (index + 1).toString().padStart(maxLineNumber, ' ');
        return `${lineNumber}: ${line}`;
      })
      .join('\n');
  }

  /**
   * Process plain text content
   */
  private processTextContent(content: string): string {
    // Basic text cleanup
    return content
      .replace(/\r\n/g, '\n')
      .replace(/\t/g, '  ')
      .trim();
  }

  /**
   * Get MIME type for file
   */
  private getMimeType(filePath: string): string {
    const extension = path.extname(filePath).toLowerCase();
    const mimeTypes: Record<string, string> = {
      '.txt': 'text/plain',
      '.md': 'text/markdown',
      '.json': 'application/json',
      '.js': 'application/javascript',
      '.ts': 'application/typescript',
      '.html': 'text/html',
      '.htm': 'text/html',
      '.css': 'text/css',
      '.csv': 'text/csv',
      '.py': 'text/x-python',
      '.java': 'text/x-java-source',
      '.cpp': 'text/x-c++src',
      '.c': 'text/x-csrc'
    };

    return mimeTypes[extension] || 'text/plain';
  }

  /**
   * Update configuration
   */
  updateConfig(config: Partial<FileConfig>): void {
    this.config = { ...this.config, ...config };
    logger.info('File reader configuration updated');
  }
}
