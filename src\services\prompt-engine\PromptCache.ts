/**
 * Prompt Cache implementation
 * Provides caching for prompt enhancement results with LRU eviction
 */

import {
  CacheEntry,
  CacheOptions,
  PromptEngineError
} from './types';
import { LoggerFactory } from '../../core/logger';

const logger = LoggerFactory.getInstance().getLogger('PromptCache');

/**
 * Prompt Cache implementation with LRU eviction
 */
export class PromptCache {
  private cache: Map<string, CacheEntry> = new Map();
  private config: CacheOptions;
  private hits: number = 0;
  private misses: number = 0;

  constructor(config: CacheOptions) {
    this.config = config;
  }

  /**
   * Get value from cache
   */
  get(key: string): string | null {
    const entry = this.cache.get(key);
    
    if (!entry) {
      this.misses++;
      return null;
    }

    // Check if entry has expired
    if (Date.now() - entry.timestamp > this.config.ttl) {
      this.cache.delete(key);
      this.misses++;
      return null;
    }

    // Update hit count and move to end (LRU)
    entry.hits++;
    this.cache.delete(key);
    this.cache.set(key, entry);
    this.hits++;

    return entry.value;
  }

  /**
   * Set value in cache
   */
  set(key: string, value: string): void {
    try {
      // Check size limits
      if (value.length > this.config.maxSize) {
        logger.warn('Value too large for cache', { 
          size: value.length, 
          maxSize: this.config.maxSize 
        });
        return;
      }

      // Remove oldest entries if at capacity
      if (this.cache.size >= this.config.maxEntries) {
        this.evictOldest();
      }

      // Check total cache size
      const totalSize = this.getTotalSize() + value.length;
      if (totalSize > this.config.maxSize) {
        this.evictBySize(value.length);
      }

      const entry: CacheEntry = {
        value,
        timestamp: Date.now(),
        hits: 0,
        size: value.length
      };

      this.cache.set(key, entry);
      
      logger.debug('Cache entry added', { 
        key: key.substring(0, 20) + '...', 
        size: value.length 
      });
    } catch (error) {
      logger.error('Failed to set cache entry', { error, key });
    }
  }

  /**
   * Check if key exists in cache
   */
  has(key: string): boolean {
    const entry = this.cache.get(key);
    
    if (!entry) {
      return false;
    }

    // Check if expired
    if (Date.now() - entry.timestamp > this.config.ttl) {
      this.cache.delete(key);
      return false;
    }

    return true;
  }

  /**
   * Delete entry from cache
   */
  delete(key: string): boolean {
    return this.cache.delete(key);
  }

  /**
   * Clear all cache entries
   */
  clear(): void {
    this.cache.clear();
    this.hits = 0;
    this.misses = 0;
    logger.info('Cache cleared');
  }

  /**
   * Get cache statistics
   */
  getStats() {
    const totalRequests = this.hits + this.misses;
    const hitRate = totalRequests > 0 ? this.hits / totalRequests : 0;

    return {
      size: this.cache.size,
      hits: this.hits,
      misses: this.misses,
      hitRate,
      totalSize: this.getTotalSize(),
      maxSize: this.config.maxSize,
      maxEntries: this.config.maxEntries,
      ttl: this.config.ttl
    };
  }

  /**
   * Get cache hit rate
   */
  getHitRate(): number {
    const totalRequests = this.hits + this.misses;
    return totalRequests > 0 ? this.hits / totalRequests : 0;
  }

  /**
   * Update cache configuration
   */
  updateConfig(config: Partial<CacheOptions>): void {
    this.config = { ...this.config, ...config };
    
    // Enforce new limits
    if (this.cache.size > this.config.maxEntries) {
      const toRemove = this.cache.size - this.config.maxEntries;
      for (let i = 0; i < toRemove; i++) {
        this.evictOldest();
      }
    }

    if (this.getTotalSize() > this.config.maxSize) {
      this.evictBySize(0);
    }

    logger.info('Cache configuration updated', this.config);
  }

  /**
   * Get total size of all cached values
   */
  private getTotalSize(): number {
    let totalSize = 0;
    for (const entry of this.cache.values()) {
      totalSize += entry.size;
    }
    return totalSize;
  }

  /**
   * Evict oldest entry (LRU)
   */
  private evictOldest(): void {
    const firstKey = this.cache.keys().next().value;
    if (firstKey) {
      this.cache.delete(firstKey);
      logger.debug('Evicted oldest cache entry', { key: firstKey.substring(0, 20) + '...' });
    }
  }

  /**
   * Evict entries by size to make room
   */
  private evictBySize(requiredSpace: number): void {
    const targetSize = this.config.maxSize - requiredSpace;
    
    while (this.getTotalSize() > targetSize && this.cache.size > 0) {
      this.evictOldest();
    }
  }

  /**
   * Clean up expired entries
   */
  cleanup(): void {
    const now = Date.now();
    const expiredKeys: string[] = [];

    for (const [key, entry] of this.cache.entries()) {
      if (now - entry.timestamp > this.config.ttl) {
        expiredKeys.push(key);
      }
    }

    for (const key of expiredKeys) {
      this.cache.delete(key);
    }

    if (expiredKeys.length > 0) {
      logger.debug('Cleaned up expired cache entries', { count: expiredKeys.length });
    }
  }

  /**
   * Get cache entries sorted by various criteria
   */
  getEntries(sortBy: 'timestamp' | 'hits' | 'size' = 'timestamp'): Array<{ key: string; entry: CacheEntry }> {
    const entries = Array.from(this.cache.entries()).map(([key, entry]) => ({ key, entry }));

    entries.sort((a, b) => {
      switch (sortBy) {
        case 'timestamp':
          return b.entry.timestamp - a.entry.timestamp;
        case 'hits':
          return b.entry.hits - a.entry.hits;
        case 'size':
          return b.entry.size - a.entry.size;
        default:
          return 0;
      }
    });

    return entries;
  }

  /**
   * Export cache data
   */
  export(): any {
    return {
      entries: Array.from(this.cache.entries()),
      stats: this.getStats(),
      config: this.config,
      timestamp: new Date().toISOString()
    };
  }

  /**
   * Import cache data
   */
  import(data: any): void {
    try {
      this.clear();
      
      if (data.entries && Array.isArray(data.entries)) {
        for (const [key, entry] of data.entries) {
          // Validate entry structure
          if (entry && typeof entry.value === 'string' && typeof entry.timestamp === 'number') {
            this.cache.set(key, entry);
          }
        }
      }

      logger.info('Cache data imported', { entries: this.cache.size });
    } catch (error) {
      logger.error('Failed to import cache data', { error });
      throw new PromptEngineError('Failed to import cache data', error);
    }
  }
}
