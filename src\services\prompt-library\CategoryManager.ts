/**
 * Category Manager for Prompt Library
 * Manages hierarchical categories and organization
 */

import { v4 as uuidv4 } from 'uuid';
import {
  Category,
  CategoryNode,
  Prompt,
  CategoryNotFoundError,
  PromptLibraryError
} from './types';
import { getStorageManager } from '../../storage';
import { LoggerFactory } from '../../core/logger';

const logger = LoggerFactory.getInstance().getLogger('CategoryManager');

/**
 * Default categories for new installations
 */
const DEFAULT_CATEGORIES: Omit<Category, 'id'>[] = [
  {
    name: 'General',
    description: 'General purpose prompts',
    color: '#6B7280',
    icon: 'folder',
    sortOrder: 0,
    metadata: { createdAt: new Date(), promptCount: 0 }
  },
  {
    name: 'Development',
    description: 'Programming and development prompts',
    color: '#10B981',
    icon: 'code',
    sortOrder: 1,
    metadata: { createdAt: new Date(), promptCount: 0 }
  },
  {
    name: 'Writing',
    description: 'Content creation and writing prompts',
    color: '#8B5CF6',
    icon: 'pencil',
    sortOrder: 2,
    metadata: { createdAt: new Date(), promptCount: 0 }
  },
  {
    name: 'Analysis',
    description: 'Data analysis and research prompts',
    color: '#F59E0B',
    icon: 'chart',
    sortOrder: 3,
    metadata: { createdAt: new Date(), promptCount: 0 }
  },
  {
    name: 'Templates',
    description: 'Reusable prompt templates',
    color: '#EF4444',
    icon: 'template',
    sortOrder: 4,
    metadata: { createdAt: new Date(), promptCount: 0 }
  }
];

/**
 * Category Manager implementation
 */
export class CategoryManager {
  /**
   * Create a new category
   */
  async createCategory(category: Omit<Category, 'id'>): Promise<string> {
    try {
      const storage = getStorageManager();
      const categoryRepository = storage.categories;

      const newCategory: Category = {
        id: uuidv4(),
        ...category,
        metadata: {
          ...category.metadata,
          createdAt: new Date(),
          promptCount: 0
        }
      };

      // Validate category
      this.validateCategory(newCategory);

      // Check for duplicate names at the same level
      await this.checkDuplicateName(newCategory.name, newCategory.parentId);

      await categoryRepository.save(newCategory);

      logger.info('Category created', { id: newCategory.id, name: newCategory.name });
      return newCategory.id;
    } catch (error) {
      logger.error('Failed to create category', { error, category: category.name });
      throw new PromptLibraryError('Failed to create category', error);
    }
  }

  /**
   * Update an existing category
   */
  async updateCategory(id: string, updates: Partial<Category>): Promise<void> {
    try {
      const storage = getStorageManager();
      const categoryRepository = storage.categories;

      const existing = await categoryRepository.findById(id);
      if (!existing) {
        throw new CategoryNotFoundError(id);
      }

      const updatedCategory: Category = {
        ...existing,
        ...updates,
        id // Ensure ID doesn't change
      };

      // Validate updated category
      this.validateCategory(updatedCategory);

      // Check for duplicate names if name is being changed
      if (updates.name && updates.name !== existing.name) {
        await this.checkDuplicateName(updates.name, updatedCategory.parentId, id);
      }

      await categoryRepository.update(id, updatedCategory);

      logger.info('Category updated', { id, updates: Object.keys(updates) });
    } catch (error) {
      logger.error('Failed to update category', { error, id, updates });
      throw new PromptLibraryError('Failed to update category', error);
    }
  }

  /**
   * Delete a category
   */
  async deleteCategory(id: string, movePromptsTo?: string): Promise<void> {
    try {
      const storage = getStorageManager();
      const categoryRepository = storage.categories;
      const promptRepository = storage.prompts;

      const existing = await categoryRepository.findById(id);
      if (!existing) {
        throw new CategoryNotFoundError(id);
      }

      // Check if category has child categories
      const children = await this.getChildCategories(id);
      if (children.length > 0) {
        throw new PromptLibraryError('Cannot delete category with child categories');
      }

      // Handle prompts in this category
      const prompts = await this.getPromptsInCategory(id, false);
      if (prompts.length > 0) {
        if (movePromptsTo) {
          // Move prompts to another category
          const targetCategory = await categoryRepository.findById(movePromptsTo);
          if (!targetCategory) {
            throw new CategoryNotFoundError(movePromptsTo);
          }

          for (const prompt of prompts) {
            await promptRepository.update(prompt.id, { category: movePromptsTo });
          }

          // Update prompt counts
          await this.updatePromptCount(movePromptsTo);
        } else {
          throw new PromptLibraryError('Category contains prompts. Specify movePromptsTo parameter.');
        }
      }

      await categoryRepository.delete(id);

      logger.info('Category deleted', { id, name: existing.name, promptsMoved: prompts.length });
    } catch (error) {
      logger.error('Failed to delete category', { error, id });
      throw new PromptLibraryError('Failed to delete category', error);
    }
  }

  /**
   * Get category tree structure
   */
  async getCategoryTree(): Promise<CategoryNode[]> {
    try {
      const storage = getStorageManager();
      const categoryRepository = storage.categories;

      const allCategories = await categoryRepository.findAll();
      
      // Build tree structure
      const categoryMap = new Map<string, Category>();
      const rootCategories: Category[] = [];

      // First pass: create map and identify root categories
      for (const category of allCategories) {
        categoryMap.set(category.id, category);
        if (!category.parentId) {
          rootCategories.push(category as any);
        }
      }

      // Second pass: build tree
      const buildTree = async (category: Category): Promise<CategoryNode> => {
        const children: CategoryNode[] = [];
        let totalPromptCount = category.metadata.promptCount;

        for (const cat of allCategories) {
          if (cat.parentId === category.id) {
            const childNode = await buildTree(cat as any);
            children.push(childNode);
            totalPromptCount += childNode.promptCount;
          }
        }

        return {
          category,
          children: children.sort((a, b) => a.category.sortOrder - b.category.sortOrder),
          promptCount: totalPromptCount
        };
      };

      const tree: CategoryNode[] = [];
      for (const rootCategory of rootCategories) {
        tree.push(await buildTree(rootCategory));
      }

      return tree.sort((a, b) => a.category.sortOrder - b.category.sortOrder);
    } catch (error) {
      logger.error('Failed to get category tree', { error });
      throw new PromptLibraryError('Failed to get category tree', error);
    }
  }

  /**
   * Get prompts in a category
   */
  async getPromptsInCategory(categoryId: string, includeSubcategories: boolean = false): Promise<Prompt[]> {
    try {
      const storage = getStorageManager();
      const promptRepository = storage.prompts;

      let categoryIds = [categoryId];

      if (includeSubcategories) {
        const subcategoryIds = await this.getAllSubcategoryIds(categoryId);
        categoryIds = categoryIds.concat(subcategoryIds);
      }

      const allPrompts = await promptRepository.findAll();
      return allPrompts.filter(prompt => categoryIds.includes(prompt.categoryId));
    } catch (error) {
      logger.error('Failed to get prompts in category', { error, categoryId });
      throw new PromptLibraryError('Failed to get prompts in category', error);
    }
  }

  /**
   * Get all categories
   */
  async getAllCategories(): Promise<Category[]> {
    try {
      const storage = getStorageManager();
      const categoryRepository = storage.categories;
      return await categoryRepository.findAll() as any;
    } catch (error) {
      logger.error('Failed to get all categories', { error });
      throw new PromptLibraryError('Failed to get all categories', error);
    }
  }

  /**
   * Initialize default categories
   */
  async initializeDefaultCategories(): Promise<void> {
    try {
      logger.info('Initializing default categories');

      for (const categoryData of DEFAULT_CATEGORIES) {
        await this.createCategory(categoryData);
      }

      logger.info('Default categories initialized', { count: DEFAULT_CATEGORIES.length });
    } catch (error) {
      logger.error('Failed to initialize default categories', { error });
      throw new PromptLibraryError('Failed to initialize default categories', error);
    }
  }

  /**
   * Update prompt count for a category
   */
  async updatePromptCount(categoryId: string): Promise<void> {
    try {
      const storage = getStorageManager();
      const categoryRepository = storage.categories;

      const category = await categoryRepository.findById(categoryId);
      if (!category) {
        return; // Category doesn't exist, skip update
      }

      const prompts = await this.getPromptsInCategory(categoryId, false);
      const promptCount = prompts.length;

      await categoryRepository.update(categoryId, {
        metadata: {
          ...category.metadata,
          promptCount
        } as any
      });

      logger.debug('Category prompt count updated', { categoryId, promptCount });
    } catch (error) {
      logger.error('Failed to update category prompt count', { error, categoryId });
      // Don't throw error for count updates to avoid breaking other operations
    }
  }

  /**
   * Validate category data
   */
  private validateCategory(category: Category): void {
    if (!category.name || category.name.trim().length === 0) {
      throw new PromptLibraryError('Category name is required');
    }

    if (category.name.length > 100) {
      throw new PromptLibraryError('Category name is too long (max 100 characters)');
    }

    if (category.description && category.description.length > 500) {
      throw new PromptLibraryError('Category description is too long (max 500 characters)');
    }

    if (category.sortOrder < 0) {
      throw new PromptLibraryError('Sort order must be non-negative');
    }
  }

  /**
   * Check for duplicate category names at the same level
   */
  private async checkDuplicateName(name: string, parentId?: string, excludeId?: string): Promise<void> {
    const storage = getStorageManager();
    const categoryRepository = storage.categories;

    const allCategories = await categoryRepository.findAll();
    
    const duplicates = allCategories.filter(cat => 
      cat.name.toLowerCase() === name.toLowerCase() &&
      cat.parentId === parentId &&
      cat.id !== excludeId
    );

    if (duplicates.length > 0) {
      throw new PromptLibraryError(`Category name '${name}' already exists at this level`);
    }
  }

  /**
   * Get child categories of a category
   */
  private async getChildCategories(parentId: string): Promise<Category[]> {
    const storage = getStorageManager();
    const categoryRepository = storage.categories;

    const allCategories = await categoryRepository.findAll();
    return allCategories.filter(cat => cat.parentId === parentId) as any;
  }

  /**
   * Get all subcategory IDs recursively
   */
  private async getAllSubcategoryIds(categoryId: string): Promise<string[]> {
    const subcategoryIds: string[] = [];
    const children = await this.getChildCategories(categoryId);

    for (const child of children) {
      subcategoryIds.push(child.id);
      const grandchildren = await this.getAllSubcategoryIds(child.id);
      subcategoryIds.push(...grandchildren);
    }

    return subcategoryIds;
  }
}
