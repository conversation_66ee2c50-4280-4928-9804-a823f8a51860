/**
 * Main Prompt Library implementation
 * Comprehensive prompt management with organization, search, and versioning
 */

import { EventEmitter } from 'events';
import { v4 as uuidv4 } from 'uuid';
import {
  IPromptLibrary,
  Prompt,
  Category,
  SearchQuery,
  SearchResult,
  PromptLibraryConfig,
  PromptLibraryError,
  PromptNotFoundError,
  CategoryNotFoundError,
  ExportOptions,
  ImportOptions,
  ImportResult,
  Tag,
  PromptAnalytics,
  ReportOptions,
  AnalyticsReport,
  CategoryNode,
  PromptVersion
} from './types';
import { SearchEngine } from './SearchEngine';
import { CategoryManager } from './CategoryManager';
import { TagManager } from './TagManager';
import { VersionManager } from './VersionManager';
import { ExportManager } from './ExportManager';
import { ImportManager } from './ImportManager';
import { AnalyticsManager } from './AnalyticsManager';
import { getStorageManager } from '../../storage';
import { LoggerFactory } from '../../core/logger';

const logger = LoggerFactory.getInstance().getLogger('PromptLibrary');

/**
 * Main Prompt Library implementation
 */
export class PromptLibrary extends EventEmitter implements IPromptLibrary {
  private config: PromptLibraryConfig;
  private searchEngine: SearchEngine;
  private categoryManager: CategoryManager;
  private tagManager: TagManager;
  private versionManager: VersionManager;
  private exportManager: ExportManager;
  private importManager: ImportManager;
  private analyticsManager: AnalyticsManager;
  private isInitialized: boolean = false;

  constructor(config?: Partial<PromptLibraryConfig>) {
    super();
    
    this.config = {
      storage: {
        provider: 'sqlite',
        encryption: false,
        ...config?.storage
      },
      search: {
        indexingEnabled: true,
        maxResults: 100,
        fuzzySearch: true,
        ...config?.search
      },
      versioning: {
        enabled: true,
        maxVersions: 10,
        autoVersion: true,
        ...config?.versioning
      },
      analytics: {
        enabled: true,
        trackUsage: true,
        trackRatings: true,
        ...config?.analytics
      }
    };

    this.initializeComponents();
  }

  /**
   * Initialize all components
   */
  private initializeComponents(): void {
    this.searchEngine = new SearchEngine(this.config.search);
    this.categoryManager = new CategoryManager();
    this.tagManager = new TagManager();
    this.versionManager = new VersionManager(this.config.versioning);
    this.exportManager = new ExportManager();
    this.importManager = new ImportManager();
    this.analyticsManager = new AnalyticsManager(this.config.analytics);
  }

  /**
   * Initialize the prompt library
   */
  async initialize(): Promise<void> {
    if (this.isInitialized) {
      return;
    }

    try {
      // Initialize storage
      const storage = getStorageManager();
      await storage.initialize();

      // Initialize default categories if none exist
      const categories = await this.categoryManager.getAllCategories();
      if (categories.length === 0) {
        await this.categoryManager.initializeDefaultCategories();
      }

      // Rebuild search index if enabled
      if (this.config.search.indexingEnabled) {
        const allPrompts = await this.loadAll();
        await this.searchEngine.rebuildIndex(allPrompts);
      }

      this.isInitialized = true;
      this.emit('initialized');
      logger.info('Prompt library initialized successfully');
    } catch (error) {
      logger.error('Failed to initialize prompt library', { error });
      throw new PromptLibraryError('Failed to initialize prompt library', error);
    }
  }

  /**
   * Save a prompt
   */
  async save(prompt: Omit<Prompt, 'id'> | Prompt): Promise<string> {
    try {
      const storage = getStorageManager();
      const promptRepository = storage.getRepository('prompts');

      // Generate ID if not provided
      const promptWithId: Prompt = {
        id: uuidv4(),
        ...prompt,
        metadata: {
          ...prompt.metadata,
          updatedAt: new Date()
        }
      } as Prompt;

      // If prompt has an ID, it's an update
      if ('id' in prompt && prompt.id) {
        promptWithId.id = prompt.id;
        
        // Create version if versioning is enabled
        if (this.config.versioning.enabled && this.config.versioning.autoVersion) {
          const existing = await this.load(prompt.id);
          if (existing && existing.content !== prompt.content) {
            await this.versionManager.createVersion(prompt.id, 'Auto-saved version');
          }
        }
      } else {
        // New prompt
        promptWithId.metadata.createdAt = new Date();
        promptWithId.version = 1;
        promptWithId.usage = {
          count: 0,
          lastUsed: new Date()
        };
      }

      // Validate prompt
      this.validatePrompt(promptWithId);

      // Save to storage
      await promptRepository.save(promptWithId);

      // Update search index
      if (this.config.search.indexingEnabled) {
        await this.searchEngine.indexPrompt(promptWithId);
      }

      // Update tag usage
      await this.tagManager.updateTagUsage();

      // Update category prompt count
      await this.categoryManager.updatePromptCount(promptWithId.category);

      this.emit('promptSaved', promptWithId);
      logger.info('Prompt saved', { id: promptWithId.id, title: promptWithId.title });

      return promptWithId.id;
    } catch (error) {
      logger.error('Failed to save prompt', { error, prompt: prompt.title });
      throw new PromptLibraryError('Failed to save prompt', error);
    }
  }

  /**
   * Load a prompt by ID
   */
  async load(id: string): Promise<Prompt | null> {
    try {
      const storage = getStorageManager();
      const promptRepository = storage.getRepository('prompts');
      
      const prompt = await promptRepository.findById(id);
      
      if (prompt) {
        this.emit('promptLoaded', prompt);
      }
      
      return prompt;
    } catch (error) {
      logger.error('Failed to load prompt', { error, id });
      throw new PromptLibraryError('Failed to load prompt', error);
    }
  }

  /**
   * Load all prompts with optional filtering
   */
  async loadAll(filter?: Partial<SearchQuery>): Promise<Prompt[]> {
    try {
      const storage = getStorageManager();
      const promptRepository = storage.getRepository('prompts');
      
      let prompts = await promptRepository.findAll();

      // Apply filters if provided
      if (filter) {
        prompts = this.applyFilters(prompts, filter);
      }

      this.emit('promptsLoaded', { count: prompts.length, filter });
      
      return prompts;
    } catch (error) {
      logger.error('Failed to load prompts', { error, filter });
      throw new PromptLibraryError('Failed to load prompts', error);
    }
  }

  /**
   * Update a prompt
   */
  async update(id: string, updates: Partial<Prompt>): Promise<void> {
    try {
      const existing = await this.load(id);
      if (!existing) {
        throw new PromptNotFoundError(id);
      }

      // Create version if content changed and versioning is enabled
      if (this.config.versioning.enabled && updates.content && updates.content !== existing.content) {
        await this.versionManager.createVersion(id, 'Updated content');
      }

      const updatedPrompt: Prompt = {
        ...existing,
        ...updates,
        id, // Ensure ID doesn't change
        metadata: {
          ...existing.metadata,
          ...updates.metadata,
          updatedAt: new Date()
        }
      };

      // Validate updated prompt
      this.validatePrompt(updatedPrompt);

      const storage = getStorageManager();
      const promptRepository = storage.getRepository('prompts');
      await promptRepository.update(id, updatedPrompt);

      // Update search index
      if (this.config.search.indexingEnabled) {
        await this.searchEngine.indexPrompt(updatedPrompt);
      }

      this.emit('promptUpdated', updatedPrompt);
      logger.info('Prompt updated', { id, updates: Object.keys(updates) });
    } catch (error) {
      logger.error('Failed to update prompt', { error, id, updates });
      throw new PromptLibraryError('Failed to update prompt', error);
    }
  }

  /**
   * Delete a prompt
   */
  async delete(id: string): Promise<void> {
    try {
      const existing = await this.load(id);
      if (!existing) {
        throw new PromptNotFoundError(id);
      }

      const storage = getStorageManager();
      const promptRepository = storage.getRepository('prompts');
      await promptRepository.delete(id);

      // Remove from search index
      if (this.config.search.indexingEnabled) {
        await this.searchEngine.removeFromIndex(id);
      }

      // Delete versions if versioning is enabled
      if (this.config.versioning.enabled) {
        const versions = await this.versionManager.getVersionHistory(id);
        for (const version of versions) {
          await this.versionManager.deleteVersion(version.id);
        }
      }

      // Update category prompt count
      await this.categoryManager.updatePromptCount(existing.category);

      this.emit('promptDeleted', { id, prompt: existing });
      logger.info('Prompt deleted', { id, title: existing.title });
    } catch (error) {
      logger.error('Failed to delete prompt', { error, id });
      throw new PromptLibraryError('Failed to delete prompt', error);
    }
  }

  /**
   * Search prompts
   */
  async search(query: SearchQuery): Promise<SearchResult[]> {
    try {
      if (this.config.search.indexingEnabled) {
        return await this.searchEngine.search(query);
      } else {
        // Fallback to simple filtering
        const allPrompts = await this.loadAll();
        const filteredPrompts = this.applyFilters(allPrompts, query);
        
        return filteredPrompts.map(prompt => ({
          prompt,
          score: 1.0,
          highlights: []
        }));
      }
    } catch (error) {
      logger.error('Failed to search prompts', { error, query });
      throw new PromptLibraryError('Failed to search prompts', error);
    }
  }

  // Category management methods
  async createCategory(category: Omit<Category, 'id'>): Promise<string> {
    return this.categoryManager.createCategory(category);
  }

  async updateCategory(id: string, updates: Partial<Category>): Promise<void> {
    return this.categoryManager.updateCategory(id, updates);
  }

  async deleteCategory(id: string, movePromptsTo?: string): Promise<void> {
    return this.categoryManager.deleteCategory(id, movePromptsTo);
  }

  async getCategoryTree(): Promise<CategoryNode[]> {
    return this.categoryManager.getCategoryTree();
  }

  async getPromptsInCategory(categoryId: string, includeSubcategories?: boolean): Promise<Prompt[]> {
    return this.categoryManager.getPromptsInCategory(categoryId, includeSubcategories);
  }

  // Tag management methods
  async createTag(name: string, options?: Partial<Tag>): Promise<void> {
    return this.tagManager.createTag(name, options);
  }

  async deleteTag(name: string): Promise<void> {
    return this.tagManager.deleteTag(name);
  }

  async renameTag(oldName: string, newName: string): Promise<void> {
    return this.tagManager.renameTag(oldName, newName);
  }

  async getPopularTags(limit: number = 20): Promise<Tag[]> {
    return this.tagManager.getPopularTags(limit);
  }

  async getTagSuggestions(partial: string): Promise<string[]> {
    return this.tagManager.getTagSuggestions(partial);
  }

  // Version management methods
  async createVersion(promptId: string, changes: string): Promise<string> {
    return this.versionManager.createVersion(promptId, changes);
  }

  async getVersionHistory(promptId: string): Promise<PromptVersion[]> {
    return this.versionManager.getVersionHistory(promptId);
  }

  async restoreVersion(promptId: string, versionId: string): Promise<void> {
    return this.versionManager.restoreVersion(promptId, versionId);
  }

  // Import/Export methods
  async exportPrompts(options: ExportOptions): Promise<string> {
    return this.exportManager.exportPrompts(options);
  }

  async exportToFile(filePath: string, options: ExportOptions): Promise<void> {
    return this.exportManager.exportToFile(filePath, options);
  }

  async importFromJSON(jsonData: string, options: ImportOptions): Promise<ImportResult> {
    return this.importManager.importFromJSON(jsonData, options);
  }

  async importFromFile(filePath: string, options: ImportOptions): Promise<ImportResult> {
    return this.importManager.importFromFile(filePath, options);
  }

  // Analytics methods
  async recordUsage(promptId: string, rating?: number, success?: boolean): Promise<void> {
    return this.analyticsManager.recordUsage(promptId, rating, success);
  }

  async getPromptAnalytics(promptId: string): Promise<PromptAnalytics> {
    return this.analyticsManager.getPromptAnalytics(promptId);
  }

  async getTopPrompts(period: 'week' | 'month' | 'year', limit: number): Promise<Prompt[]> {
    return this.analyticsManager.getTopPrompts(period, limit);
  }

  async generateReport(options: ReportOptions): Promise<AnalyticsReport> {
    return this.analyticsManager.generateReport(options);
  }

  /**
   * Validate prompt data
   */
  private validatePrompt(prompt: Prompt): void {
    if (!prompt.title || prompt.title.trim().length === 0) {
      throw new PromptLibraryError('Prompt title is required');
    }

    if (!prompt.content || prompt.content.trim().length === 0) {
      throw new PromptLibraryError('Prompt content is required');
    }

    if (!prompt.category || prompt.category.trim().length === 0) {
      throw new PromptLibraryError('Prompt category is required');
    }

    // Validate variables if present
    if (prompt.variables) {
      for (const variable of prompt.variables) {
        if (!variable.name || variable.name.trim().length === 0) {
          throw new PromptLibraryError('Variable name is required');
        }
      }
    }
  }

  /**
   * Apply filters to prompts
   */
  private applyFilters(prompts: Prompt[], filter: Partial<SearchQuery>): Prompt[] {
    let filtered = prompts;

    if (filter.category) {
      filtered = filtered.filter(p => p.category === filter.category);
    }

    if (filter.tags && filter.tags.length > 0) {
      filtered = filtered.filter(p => 
        filter.tags!.some(tag => p.tags.includes(tag))
      );
    }

    if (filter.isTemplate !== undefined) {
      filtered = filtered.filter(p => p.isTemplate === filter.isTemplate);
    }

    if (filter.isFavorite !== undefined) {
      filtered = filtered.filter(p => p.isFavorite === filter.isFavorite);
    }

    if (filter.author) {
      filtered = filtered.filter(p => p.metadata.author === filter.author);
    }

    if (filter.dateRange) {
      filtered = filtered.filter(p => 
        p.metadata.createdAt >= filter.dateRange!.start &&
        p.metadata.createdAt <= filter.dateRange!.end
      );
    }

    // Apply sorting
    if (filter.sortBy) {
      filtered = this.sortPrompts(filtered, filter.sortBy, filter.sortOrder || 'asc');
    }

    // Apply pagination
    if (filter.offset || filter.limit) {
      const offset = filter.offset || 0;
      const limit = filter.limit || filtered.length;
      filtered = filtered.slice(offset, offset + limit);
    }

    return filtered;
  }

  /**
   * Sort prompts by specified criteria
   */
  private sortPrompts(prompts: Prompt[], sortBy: string, order: 'asc' | 'desc'): Prompt[] {
    return prompts.sort((a, b) => {
      let comparison = 0;

      switch (sortBy) {
        case 'title':
          comparison = a.title.localeCompare(b.title);
          break;
        case 'date':
          comparison = a.metadata.createdAt.getTime() - b.metadata.createdAt.getTime();
          break;
        case 'usage':
          comparison = a.usage.count - b.usage.count;
          break;
        case 'rating':
          comparison = (a.usage.averageRating || 0) - (b.usage.averageRating || 0);
          break;
        default:
          comparison = 0;
      }

      return order === 'desc' ? -comparison : comparison;
    });
  }
}

// Global instance management
let globalPromptLibrary: PromptLibrary | null = null;

export const getPromptLibrary = (): PromptLibrary => {
  if (!globalPromptLibrary) {
    globalPromptLibrary = new PromptLibrary();
  }
  return globalPromptLibrary;
};

export const initializePromptLibrary = async (config?: Partial<PromptLibraryConfig>): Promise<PromptLibrary> => {
  globalPromptLibrary = new PromptLibrary(config);
  await globalPromptLibrary.initialize();
  return globalPromptLibrary;
};
