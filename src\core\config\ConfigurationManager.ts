/**
 * Main Configuration Manager implementation
 * Provides centralized configuration management with validation, persistence, and real-time updates
 */

import * as path from 'path';
import { EventEmitter } from 'events';
import {
  ConfigurationStore,
  ConfigurationSchema,
  ConfigValidator,
  ConfigChangeListener,
  ConfigChangeEvent,
  ValidationResult,
  ConfigurationManagerOptions,
  ExportOptions,
  ImportOptions
} from './types';
import { FileConfigurationStore } from './ConfigurationStore';
import { DEFAULT_CONFIG_SCHEMA, getDefaultConfig, getSettingByPath } from './ConfigurationSchema';
import { ValidatorFactory } from './validators';
import { LoggerFactory } from '../logger';

const logger = LoggerFactory.getInstance().getLogger('ConfigurationManager');

export class ConfigurationManager extends EventEmitter {
  private store: ConfigurationStore;
  private schema: ConfigurationSchema;
  private validators: Map<string, ConfigValidator> = new Map();
  private listeners: Map<string, ConfigChangeListener[]> = new Map();
  private options: ConfigurationManagerOptions;
  private isLoaded: boolean = false;

  constructor(options: ConfigurationManagerOptions = {}) {
    super();
    
    this.options = {
      configPath: options.configPath || path.join(process.cwd(), 'config', 'app.json'),
      schema: options.schema || DEFAULT_CONFIG_SCHEMA,
      autoSave: options.autoSave ?? true,
      watchChanges: options.watchChanges ?? true,
      backupCount: options.backupCount ?? 5,
      validateOnSet: options.validateOnSet ?? true,
      ...options
    };

    this.schema = this.options.schema!;
    this.store = new FileConfigurationStore(this.options.configPath!);
    
    this.initializeValidators();
    this.setupFileWatcher();
  }

  /**
   * Load configuration from storage
   */
  async load(): Promise<void> {
    try {
      logger.info('Loading configuration', { path: this.options.configPath });
      
      await this.store.load();
      this.isLoaded = true;
      
      // Validate loaded configuration
      const validationResult = this.validateAll();
      if (!validationResult.valid) {
        logger.warn('Configuration validation failed', { 
          errors: validationResult.errors,
          warnings: validationResult.warnings 
        });
      }
      
      this.emit('loaded');
      logger.info('Configuration loaded successfully');
    } catch (error) {
      logger.error('Failed to load configuration', error);
      throw error;
    }
  }

  /**
   * Save configuration to storage
   */
  async save(): Promise<void> {
    try {
      if (!this.isLoaded) {
        throw new Error('Configuration not loaded');
      }
      
      logger.debug('Saving configuration');
      await this.store.save(this.getAllSettings());
      this.emit('saved');
      logger.debug('Configuration saved successfully');
    } catch (error) {
      logger.error('Failed to save configuration', error);
      throw error;
    }
  }

  /**
   * Get configuration value by key
   */
  get<T>(key: string): T {
    if (!this.isLoaded) {
      throw new Error('Configuration not loaded');
    }
    
    const value = this.store.get(key);
    
    // Return default value if not found
    if (value === undefined) {
      try {
        const setting = getSettingByPath(key);
        return setting.defaultValue as T;
      } catch {
        return undefined as T;
      }
    }
    
    return value as T;
  }

  /**
   * Set configuration value by key
   */
  async set<T>(key: string, value: T): Promise<void> {
    if (!this.isLoaded) {
      throw new Error('Configuration not loaded');
    }

    const oldValue = this.get(key);
    
    // Validate if enabled
    if (this.options.validateOnSet) {
      const validationResult = this.validate(key, value);
      if (!validationResult.valid) {
        throw new Error(`Validation failed: ${validationResult.errors.join(', ')}`);
      }
    }

    // Set the value
    await this.store.set(key, value);
    
    // Auto-save if enabled
    if (this.options.autoSave) {
      await this.save();
    }

    // Notify listeners
    const changeEvent: ConfigChangeEvent = {
      key,
      oldValue,
      newValue: value,
      timestamp: new Date(),
      source: 'user'
    };
    
    this.notifyListeners(key, changeEvent);
    this.emit('changed', changeEvent);
    
    logger.debug('Configuration value changed', { key, oldValue, newValue: value });
  }

  /**
   * Reset configuration value to default
   */
  async reset(key?: string): Promise<void> {
    if (!this.isLoaded) {
      throw new Error('Configuration not loaded');
    }

    if (key) {
      // Reset specific key
      try {
        const setting = getSettingByPath(key);
        await this.set(key, setting.defaultValue);
        logger.info('Configuration key reset to default', { key });
      } catch (error) {
        throw new Error(`Failed to reset key ${key}: ${error}`);
      }
    } else {
      // Reset all configuration
      const defaultConfig = getDefaultConfig();
      await this.store.save(defaultConfig);
      
      if (this.options.autoSave) {
        await this.save();
      }
      
      this.emit('reset');
      logger.info('Configuration reset to defaults');
    }
  }

  /**
   * Validate configuration value
   */
  validate(key: string, value: any): ValidationResult {
    try {
      const setting = getSettingByPath(key);
      const validator = this.getValidator(key);
      return validator.validate(value, setting);
    } catch (error) {
      return {
        valid: false,
        errors: [`Invalid configuration key: ${key}`],
        warnings: []
      };
    }
  }

  /**
   * Validate all configuration values
   */
  validateAll(): ValidationResult {
    const result: ValidationResult = {
      valid: true,
      errors: [],
      warnings: []
    };

    for (const [sectionName, section] of Object.entries(this.schema.sections)) {
      for (const settingKey of Object.keys(section.settings)) {
        const key = `${sectionName}.${settingKey}`;
        const value = this.get(key);
        const validation = this.validate(key, value);
        
        if (!validation.valid) {
          result.valid = false;
          result.errors.push(...validation.errors);
        }
        
        result.warnings.push(...validation.warnings);
      }
    }

    return result;
  }

  /**
   * Register change listener for specific key
   */
  onChange(key: string, listener: ConfigChangeListener): () => void {
    if (!this.listeners.has(key)) {
      this.listeners.set(key, []);
    }
    
    this.listeners.get(key)!.push(listener);
    
    // Return unsubscribe function
    return () => {
      const listeners = this.listeners.get(key);
      if (listeners) {
        const index = listeners.indexOf(listener);
        if (index > -1) {
          listeners.splice(index, 1);
        }
      }
    };
  }

  /**
   * Export configuration
   */
  async export(options: ExportOptions = { format: 'json', includeDefaults: false, includeSensitive: false }): Promise<string> {
    const config = this.getAllSettings();
    
    // Filter sections if specified
    let exportData = config;
    if (options.sections) {
      exportData = {};
      for (const section of options.sections) {
        if (config[section]) {
          exportData[section] = config[section];
        }
      }
    }

    // Remove sensitive data if not included
    if (!options.includeSensitive) {
      exportData = this.removeSensitiveData(exportData);
    }

    switch (options.format) {
      case 'json':
        return JSON.stringify(exportData, null, options.minify ? 0 : 2);
      case 'yaml':
        // Would need yaml library
        throw new Error('YAML export not implemented');
      case 'env':
        return this.convertToEnvFormat(exportData);
      default:
        throw new Error(`Unsupported export format: ${options.format}`);
    }
  }

  /**
   * Import configuration
   */
  async import(data: string, options: ImportOptions = { format: 'json', merge: true, validate: true, backup: true }): Promise<void> {
    if (options.backup) {
      await this.store.backup();
    }

    let importData: any;
    
    try {
      switch (options.format) {
        case 'json':
          importData = JSON.parse(data);
          break;
        case 'yaml':
          throw new Error('YAML import not implemented');
        case 'env':
          importData = this.parseEnvFormat(data);
          break;
        default:
          throw new Error(`Unsupported import format: ${options.format}`);
      }
    } catch (error) {
      throw new Error(`Failed to parse import data: ${error}`);
    }

    if (options.validate) {
      // Validate imported data
      // Implementation would validate against schema
    }

    if (options.merge) {
      // Merge with existing configuration
      const currentConfig = this.getAllSettings();
      importData = { ...currentConfig, ...importData };
    }

    await this.store.save(importData);
    await this.load(); // Reload to apply changes
    
    this.emit('imported');
    logger.info('Configuration imported successfully');
  }

  /**
   * Get all configuration settings
   */
  private getAllSettings(): any {
    const config: any = {};
    
    for (const [sectionName, section] of Object.entries(this.schema.sections)) {
      config[sectionName] = {};
      for (const settingKey of Object.keys(section.settings)) {
        const key = `${sectionName}.${settingKey}`;
        config[sectionName][settingKey] = this.get(key);
      }
    }
    
    return config;
  }

  /**
   * Initialize validators for all settings
   */
  private initializeValidators(): void {
    for (const [sectionName, section] of Object.entries(this.schema.sections)) {
      for (const [settingKey, setting] of Object.entries(section.settings)) {
        const key = `${sectionName}.${settingKey}`;
        const validator = ValidatorFactory.createValidator(setting);
        this.validators.set(key, validator);
      }
    }
  }

  /**
   * Get validator for specific key
   */
  private getValidator(key: string): ConfigValidator {
    return this.validators.get(key) || ValidatorFactory.createValidator(getSettingByPath(key));
  }

  /**
   * Setup file watcher for configuration changes
   */
  private setupFileWatcher(): void {
    if (this.options.watchChanges) {
      this.store.watch((event) => {
        this.emit('file-changed', event);
        logger.debug('Configuration file changed externally', event);
      });
    }
  }

  /**
   * Notify change listeners
   */
  private notifyListeners(key: string, event: ConfigChangeEvent): void {
    const listeners = this.listeners.get(key);
    if (listeners) {
      for (const listener of listeners) {
        try {
          listener(event);
        } catch (error) {
          logger.error('Error in configuration change listener', error);
        }
      }
    }
  }

  /**
   * Remove sensitive data from configuration
   */
  private removeSensitiveData(config: any): any {
    // Implementation would remove sensitive fields based on schema
    return config;
  }

  /**
   * Convert configuration to environment variable format
   */
  private convertToEnvFormat(config: any): string {
    // Implementation would convert nested object to ENV format
    return '';
  }

  /**
   * Parse environment variable format
   */
  private parseEnvFormat(data: string): any {
    // Implementation would parse ENV format to nested object
    return {};
  }
}
