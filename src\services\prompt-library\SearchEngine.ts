/**
 * Search Engine for Prompt Library
 * Provides full-text search, filtering, and relevance scoring
 */

import {
  Prompt,
  SearchQuery,
  SearchResult,
  SearchHighlight,
  SearchIndex
} from './types';
import { LoggerFactory } from '../../core/logger';

const logger = LoggerFactory.getInstance().getLogger('SearchEngine');

export interface SearchConfig {
  indexingEnabled: boolean;
  maxResults: number;
  fuzzySearch: boolean;
}

/**
 * Search Engine implementation
 */
export class SearchEngine {
  private index: Map<string, SearchIndex> = new Map();
  private config: SearchConfig;

  constructor(config: SearchConfig) {
    this.config = config;
  }

  /**
   * Index a prompt for searching
   */
  async indexPrompt(prompt: Prompt): Promise<void> {
    try {
      const tokens = this.tokenizePrompt(prompt);
      const fields = {
        title: prompt.title,
        content: prompt.content,
        description: prompt.description || '',
        tags: prompt.tags.join(' '),
        category: prompt.category
      };

      const searchIndex: SearchIndex = {
        promptId: prompt.id,
        tokens,
        fields
      };

      this.index.set(prompt.id, searchIndex);
      
      logger.debug('Prompt indexed', { promptId: prompt.id, tokenCount: tokens.length });
    } catch (error) {
      logger.error('Failed to index prompt', { error, promptId: prompt.id });
      throw error;
    }
  }

  /**
   * Remove prompt from search index
   */
  async removeFromIndex(promptId: string): Promise<void> {
    try {
      this.index.delete(promptId);
      logger.debug('Prompt removed from index', { promptId });
    } catch (error) {
      logger.error('Failed to remove prompt from index', { error, promptId });
      throw error;
    }
  }

  /**
   * Search prompts based on query
   */
  async search(query: SearchQuery): Promise<SearchResult[]> {
    try {
      const results: SearchResult[] = [];
      const searchTerms = query.text ? this.tokenize(query.text) : [];

      // Get all indexed prompts
      for (const [promptId, searchIndex] of this.index.entries()) {
        const score = this.calculateRelevanceScore(searchIndex, query, searchTerms);
        
        if (score > 0) {
          // Get the actual prompt (this would normally come from storage)
          const prompt = await this.getPromptById(promptId);
          if (prompt && this.matchesFilters(prompt, query)) {
            const highlights = this.generateHighlights(searchIndex, searchTerms);
            
            results.push({
              prompt,
              score,
              highlights
            });
          }
        }
      }

      // Sort by relevance score
      results.sort((a, b) => b.score - a.score);

      // Apply sorting if specified
      if (query.sortBy && query.sortBy !== 'relevance') {
        this.sortResults(results, query.sortBy, query.sortOrder || 'asc');
      }

      // Apply pagination
      const limit = Math.min(query.limit || this.config.maxResults, this.config.maxResults);
      const offset = query.offset || 0;
      
      const paginatedResults = results.slice(offset, offset + limit);
      
      logger.info('Search completed', { 
        query: query.text, 
        totalResults: results.length, 
        returnedResults: paginatedResults.length 
      });

      return paginatedResults;
    } catch (error) {
      logger.error('Search failed', { error, query });
      throw error;
    }
  }

  /**
   * Rebuild the entire search index
   */
  async rebuildIndex(prompts: Prompt[]): Promise<void> {
    try {
      this.index.clear();
      
      for (const prompt of prompts) {
        await this.indexPrompt(prompt);
      }
      
      logger.info('Search index rebuilt', { promptCount: prompts.length });
    } catch (error) {
      logger.error('Failed to rebuild search index', { error });
      throw error;
    }
  }

  /**
   * Tokenize a prompt into searchable terms
   */
  private tokenizePrompt(prompt: Prompt): string[] {
    const text = [
      prompt.title,
      prompt.content,
      prompt.description || '',
      prompt.tags.join(' '),
      prompt.category
    ].join(' ');

    return this.tokenize(text);
  }

  /**
   * Tokenize text into searchable terms
   */
  private tokenize(text: string): string[] {
    return text
      .toLowerCase()
      .replace(/[^\w\s]/g, ' ') // Replace punctuation with spaces
      .split(/\s+/)
      .filter(token => token.length > 2) // Filter out short tokens
      .filter(token => !this.isStopWord(token)); // Filter out stop words
  }

  /**
   * Check if a word is a stop word
   */
  private isStopWord(word: string): boolean {
    const stopWords = new Set([
      'the', 'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for',
      'of', 'with', 'by', 'is', 'are', 'was', 'were', 'be', 'been', 'have',
      'has', 'had', 'do', 'does', 'did', 'will', 'would', 'could', 'should',
      'may', 'might', 'must', 'can', 'this', 'that', 'these', 'those'
    ]);
    
    return stopWords.has(word);
  }

  /**
   * Calculate relevance score for a search index
   */
  private calculateRelevanceScore(
    searchIndex: SearchIndex, 
    query: SearchQuery, 
    searchTerms: string[]
  ): number {
    if (!query.text || searchTerms.length === 0) {
      return 1.0; // Return base score if no text query
    }

    let score = 0;
    const fields = searchIndex.fields;

    // Title matches (highest weight)
    const titleMatches = this.countMatches(fields.title, searchTerms);
    score += titleMatches * 3.0;

    // Content matches (medium weight)
    const contentMatches = this.countMatches(fields.content, searchTerms);
    score += contentMatches * 1.0;

    // Tag matches (high weight)
    const tagMatches = this.countMatches(fields.tags, searchTerms);
    score += tagMatches * 2.0;

    // Description matches (low weight)
    const descriptionMatches = this.countMatches(fields.description, searchTerms);
    score += descriptionMatches * 0.5;

    // Category matches (medium weight)
    const categoryMatches = this.countMatches(fields.category, searchTerms);
    score += categoryMatches * 1.5;

    // Fuzzy matching if enabled
    if (this.config.fuzzySearch && score === 0) {
      score = this.calculateFuzzyScore(searchIndex, searchTerms);
    }

    return score;
  }

  /**
   * Count exact matches of search terms in text
   */
  private countMatches(text: string, searchTerms: string[]): number {
    const textTokens = this.tokenize(text);
    let matches = 0;

    for (const term of searchTerms) {
      matches += textTokens.filter(token => token.includes(term)).length;
    }

    return matches;
  }

  /**
   * Calculate fuzzy matching score
   */
  private calculateFuzzyScore(searchIndex: SearchIndex, searchTerms: string[]): number {
    let score = 0;
    const allText = Object.values(searchIndex.fields).join(' ');
    const textTokens = this.tokenize(allText);

    for (const term of searchTerms) {
      for (const token of textTokens) {
        const similarity = this.calculateStringSimilarity(term, token);
        if (similarity > 0.7) { // Threshold for fuzzy matching
          score += similarity * 0.5; // Lower weight for fuzzy matches
        }
      }
    }

    return score;
  }

  /**
   * Calculate string similarity using Levenshtein distance
   */
  private calculateStringSimilarity(str1: string, str2: string): number {
    const matrix = Array(str2.length + 1).fill(null).map(() => Array(str1.length + 1).fill(null));

    for (let i = 0; i <= str1.length; i++) {
      matrix[0][i] = i;
    }

    for (let j = 0; j <= str2.length; j++) {
      matrix[j][0] = j;
    }

    for (let j = 1; j <= str2.length; j++) {
      for (let i = 1; i <= str1.length; i++) {
        const indicator = str1[i - 1] === str2[j - 1] ? 0 : 1;
        matrix[j][i] = Math.min(
          matrix[j][i - 1] + 1, // deletion
          matrix[j - 1][i] + 1, // insertion
          matrix[j - 1][i - 1] + indicator // substitution
        );
      }
    }

    const distance = matrix[str2.length][str1.length];
    const maxLength = Math.max(str1.length, str2.length);
    
    return maxLength === 0 ? 1 : 1 - (distance / maxLength);
  }

  /**
   * Check if prompt matches query filters
   */
  private matchesFilters(prompt: Prompt, query: SearchQuery): boolean {
    if (query.category && prompt.category !== query.category) {
      return false;
    }

    if (query.tags && query.tags.length > 0) {
      const hasMatchingTag = query.tags.some(tag => prompt.tags.includes(tag));
      if (!hasMatchingTag) {
        return false;
      }
    }

    if (query.isTemplate !== undefined && prompt.isTemplate !== query.isTemplate) {
      return false;
    }

    if (query.isFavorite !== undefined && prompt.isFavorite !== query.isFavorite) {
      return false;
    }

    if (query.author && prompt.metadata.author !== query.author) {
      return false;
    }

    if (query.dateRange) {
      const createdAt = prompt.metadata.createdAt;
      if (createdAt < query.dateRange.start || createdAt > query.dateRange.end) {
        return false;
      }
    }

    if (query.minRating && (!prompt.usage.averageRating || prompt.usage.averageRating < query.minRating)) {
      return false;
    }

    return true;
  }

  /**
   * Generate search highlights
   */
  private generateHighlights(searchIndex: SearchIndex, searchTerms: string[]): SearchHighlight[] {
    const highlights: SearchHighlight[] = [];

    if (searchTerms.length === 0) {
      return highlights;
    }

    // Generate highlights for each field
    for (const [fieldName, fieldValue] of Object.entries(searchIndex.fields)) {
      const fieldHighlights = this.highlightField(fieldValue, searchTerms, fieldName as any);
      highlights.push(...fieldHighlights);
    }

    return highlights;
  }

  /**
   * Highlight matches in a specific field
   */
  private highlightField(
    text: string, 
    searchTerms: string[], 
    field: 'title' | 'content' | 'description' | 'tags'
  ): SearchHighlight[] {
    const highlights: SearchHighlight[] = [];
    const lowerText = text.toLowerCase();

    for (const term of searchTerms) {
      let startIndex = 0;
      
      while (true) {
        const index = lowerText.indexOf(term, startIndex);
        if (index === -1) break;

        highlights.push({
          field,
          text: text.substring(index, index + term.length),
          startIndex: index,
          endIndex: index + term.length
        });

        startIndex = index + 1;
      }
    }

    return highlights;
  }

  /**
   * Sort search results
   */
  private sortResults(results: SearchResult[], sortBy: string, order: 'asc' | 'desc'): void {
    results.sort((a, b) => {
      let comparison = 0;

      switch (sortBy) {
        case 'title':
          comparison = a.prompt.title.localeCompare(b.prompt.title);
          break;
        case 'date':
          comparison = a.prompt.metadata.createdAt.getTime() - b.prompt.metadata.createdAt.getTime();
          break;
        case 'usage':
          comparison = a.prompt.usage.count - b.prompt.usage.count;
          break;
        case 'rating':
          comparison = (a.prompt.usage.averageRating || 0) - (b.prompt.usage.averageRating || 0);
          break;
        default:
          comparison = 0;
      }

      return order === 'desc' ? -comparison : comparison;
    });
  }

  /**
   * Get prompt by ID (placeholder - would normally use storage)
   */
  private async getPromptById(promptId: string): Promise<Prompt | null> {
    // This is a placeholder - in the real implementation, this would
    // fetch the prompt from the storage layer
    try {
      const { getStorageManager } = await import('../../storage');
      const storage = getStorageManager();
      const promptRepository = storage.getRepository('prompts');
      return await promptRepository.findById(promptId);
    } catch (error) {
      logger.error('Failed to get prompt by ID', { error, promptId });
      return null;
    }
  }
}
