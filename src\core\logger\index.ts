/**
 * Logger module exports
 * Comprehensive logging system for PromptPilot Desktop
 */

// Core logger classes
export { Logger } from './Logger';
export { LoggerFactory } from './LoggerFactory';
export { PerformanceLogger } from './PerformanceLogger';

// Transport implementations
export { FileTransport } from './FileTransport';
export { ConsoleTransport } from './ConsoleTransport';

// Formatters
export { DefaultFormatter, JSONFormatter, CompactFormatter } from './formatters';

// Filters
export {
  LevelFilter,
  ModuleFilter,
  RateLimitFilter,
  TagFilter,
  ContextFilter
} from './filters';

// Types and interfaces
export * from './types';

// Convenience functions
export const createLogger = (name: string, context?: any) => {
  return LoggerFactory.getInstance().getLogger(name, context);
};

export const createPerformanceLogger = (name: string, context?: any) => {
  return LoggerFactory.getInstance().getPerformanceLogger(name, context);
};

export const configureLogging = (config: any) => {
  LoggerFactory.getInstance().configure(config);
};

// Default logger instance for quick usage
export const getDefaultLogger = () => LoggerFactory.getInstance().getLogger('app');

// Default logger instance export
export const logger = LoggerFactory.getInstance().getLogger('app');
