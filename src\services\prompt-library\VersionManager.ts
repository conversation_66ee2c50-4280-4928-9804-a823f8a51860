/**
 * Version Manager for Prompt Library
 * Manages prompt versioning and history
 */

import { v4 as uuidv4 } from 'uuid';
import {
  PromptVersion,
  VersionDiff,
  DiffChunk,
  PromptLibraryError
} from './types';
import { getStorageManager } from '../../storage';
import { LoggerFactory } from '../../core/logger';

const logger = LoggerFactory.getInstance().getLogger('VersionManager');

export interface VersionConfig {
  enabled: boolean;
  maxVersions: number;
  autoVersion: boolean;
}

/**
 * Version Manager implementation
 */
export class VersionManager {
  private config: VersionConfig;

  constructor(config: VersionConfig) {
    this.config = config;
  }

  /**
   * Create a new version of a prompt
   */
  async createVersion(promptId: string, changes: string): Promise<string> {
    if (!this.config.enabled) {
      throw new PromptLibraryError('Versioning is disabled');
    }

    try {
      const storage = getStorageManager();
      const promptRepository = storage.prompts;
      const versionRepository = storage.system; // Using system repository for versions

      // Get current prompt
      const prompt = await promptRepository.findById(promptId);
      if (!prompt) {
        throw new PromptLibraryError(`Prompt not found: ${promptId}`);
      }

      // Get current version number
      const existingVersions = await versionRepository.findAll({ promptId });
      const nextVersion = Math.max(0, ...existingVersions.map(v => v.version)) + 1;

      // Create version record
      const version: PromptVersion = {
        id: uuidv4(),
        promptId,
        version: nextVersion,
        content: prompt.content,
        title: prompt.title,
        changes,
        createdAt: new Date(),
        createdBy: prompt.metadata.author
      };

      await versionRepository.save(version);

      // Clean up old versions if needed
      await this.cleanupOldVersions(promptId);

      logger.info('Version created', { promptId, version: nextVersion, changes });
      return version.id;
    } catch (error) {
      logger.error('Failed to create version', { error, promptId, changes });
      throw new PromptLibraryError('Failed to create version', error);
    }
  }

  /**
   * Get version history for a prompt
   */
  async getVersionHistory(promptId: string): Promise<PromptVersion[]> {
    try {
      const storage = getStorageManager();
      const versionRepository = storage.system; // Using system repository for versions

      const versions = await versionRepository.findAll({ promptId });
      return versions.sort((a, b) => b.version - a.version); // Latest first
    } catch (error) {
      logger.error('Failed to get version history', { error, promptId });
      throw new PromptLibraryError('Failed to get version history', error);
    }
  }

  /**
   * Restore a prompt to a specific version
   */
  async restoreVersion(promptId: string, versionId: string): Promise<void> {
    try {
      const storage = getStorageManager();
      const promptRepository = storage.prompts;
      const versionRepository = storage.system; // Using system repository for versions

      // Get the version to restore
      const version = await versionRepository.findById(versionId);
      if (!version || version.promptId !== promptId) {
        throw new PromptLibraryError(`Version not found: ${versionId}`);
      }

      // Get current prompt
      const prompt = await promptRepository.findById(promptId);
      if (!prompt) {
        throw new PromptLibraryError(`Prompt not found: ${promptId}`);
      }

      // Create a version of current state before restoring
      await this.createVersion(promptId, `Backup before restoring to version ${version.version}`);

      // Restore the prompt
      await promptRepository.update(promptId, {
        content: version.content,
        title: version.title,
        metadata: {
          ...prompt.metadata,
          updatedAt: new Date()
        }
      });

      logger.info('Version restored', { promptId, versionId, restoredVersion: version.version });
    } catch (error) {
      logger.error('Failed to restore version', { error, promptId, versionId });
      throw new PromptLibraryError('Failed to restore version', error);
    }
  }

  /**
   * Compare two versions
   */
  async compareVersions(versionId1: string, versionId2: string): Promise<VersionDiff> {
    try {
      const storage = getStorageManager();
      const versionRepository = storage.system; // Using system repository for versions

      const version1 = await versionRepository.findById(versionId1);
      const version2 = await versionRepository.findById(versionId2);

      if (!version1 || !version2) {
        throw new PromptLibraryError('One or both versions not found');
      }

      return this.generateDiff(version1.content, version2.content);
    } catch (error) {
      logger.error('Failed to compare versions', { error, versionId1, versionId2 });
      throw new PromptLibraryError('Failed to compare versions', error);
    }
  }

  /**
   * Delete a specific version
   */
  async deleteVersion(versionId: string): Promise<void> {
    try {
      const storage = getStorageManager();
      const versionRepository = storage.system; // Using system repository for versions

      await versionRepository.delete(versionId);

      logger.info('Version deleted', { versionId });
    } catch (error) {
      logger.error('Failed to delete version', { error, versionId });
      throw new PromptLibraryError('Failed to delete version', error);
    }
  }

  /**
   * Clean up old versions beyond the maximum limit
   */
  private async cleanupOldVersions(promptId: string): Promise<void> {
    if (this.config.maxVersions <= 0) {
      return; // No limit
    }

    try {
      const storage = getStorageManager();
      const versionRepository = storage.system; // Using system repository for versions

      const versions = await versionRepository.findAll({ promptId });
      const sortedVersions = versions.sort((a, b) => b.version - a.version);

      if (sortedVersions.length > this.config.maxVersions) {
        const versionsToDelete = sortedVersions.slice(this.config.maxVersions);
        
        for (const version of versionsToDelete) {
          await versionRepository.delete(version.id);
        }

        logger.debug('Old versions cleaned up', { 
          promptId, 
          deletedCount: versionsToDelete.length,
          remainingCount: this.config.maxVersions
        });
      }
    } catch (error) {
      logger.error('Failed to cleanup old versions', { error, promptId });
      // Don't throw error for cleanup failures
    }
  }

  /**
   * Generate diff between two text contents
   */
  private generateDiff(content1: string, content2: string): VersionDiff {
    const lines1 = content1.split('\n');
    const lines2 = content2.split('\n');

    const additions: DiffChunk[] = [];
    const deletions: DiffChunk[] = [];
    const modifications: DiffChunk[] = [];

    // Simple line-by-line diff algorithm
    const maxLines = Math.max(lines1.length, lines2.length);
    
    for (let i = 0; i < maxLines; i++) {
      const line1 = lines1[i];
      const line2 = lines2[i];

      if (line1 === undefined && line2 !== undefined) {
        // Addition
        additions.push({
          type: 'addition',
          content: line2,
          lineNumber: i + 1
        });
      } else if (line1 !== undefined && line2 === undefined) {
        // Deletion
        deletions.push({
          type: 'deletion',
          content: line1,
          lineNumber: i + 1
        });
      } else if (line1 !== line2) {
        // Modification
        modifications.push({
          type: 'modification',
          content: `- ${line1}\n+ ${line2}`,
          lineNumber: i + 1
        });
      }
    }

    return {
      additions,
      deletions,
      modifications
    };
  }
}
