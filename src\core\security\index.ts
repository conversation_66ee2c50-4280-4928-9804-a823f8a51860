/**
 * Security module exports
 * Comprehensive security features for PromptPilot Desktop
 */

// Core security classes
export { SecurityManager } from './SecurityManager';
export { AESGCMEncryptionService, EncryptionUtils } from './Encryption';
export { FileSecureKeyStore, SecureDataStorage } from './SecureStorage';

// Types and interfaces
export * from './types';

// Convenience functions
let globalSecurityManager: SecurityManager | null = null;

export const getSecurityManager = (): SecurityManager => {
  if (!globalSecurityManager) {
    globalSecurityManager = new SecurityManager();
  }
  return globalSecurityManager;
};

export const initializeSecurity = async (config?: any, masterPassword?: string): Promise<SecurityManager> => {
  globalSecurityManager = new SecurityManager(config);
  await globalSecurityManager.initialize(masterPassword);
  return globalSecurityManager;
};

export const encrypt = async (data: string | Buffer, context?: any): Promise<any> => {
  return getSecurityManager().encrypt(data, context);
};

export const decrypt = async (encryptedData: any, context?: any): Promise<string | Buffer> => {
  return getSecurityManager().decrypt(encryptedData, context);
};

export const secureStore = async (key: string, value: any): Promise<void> => {
  return getSecurityManager().secureStore(key, value);
};

export const secureRetrieve = async (key: string): Promise<any> => {
  return getSecurityManager().secureRetrieve(key);
};

export const sanitizeData = async (data: any): Promise<any> => {
  return getSecurityManager().sanitizeData(data);
};

export const generateSecureToken = (options?: any): string => {
  return getSecurityManager().generateSecureToken(options);
};

export const hashPassword = async (password: string, options?: any): Promise<string> => {
  return getSecurityManager().hashPassword(password, options);
};

export const verifyPassword = async (password: string, hash: string): Promise<boolean> => {
  return getSecurityManager().verifyPassword(password, hash);
};
