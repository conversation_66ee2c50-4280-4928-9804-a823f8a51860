/**
 * Performance logging utilities
 * Provides timing and memory usage measurement capabilities
 */

import { Logger } from './Logger';
import { PerformanceInfo } from './types';

export class PerformanceLogger {
  private timers: Map<string, number> = new Map();
  private logger: Logger;

  constructor(logger: Logger) {
    this.logger = logger;
  }

  /**
   * Start a performance timer
   */
  time(label: string): void {
    this.timers.set(label, performance.now());
  }

  /**
   * End a performance timer and log the result
   */
  timeEnd(label: string): number {
    const startTime = this.timers.get(label);
    if (!startTime) {
      this.logger.warn(`Timer '${label}' does not exist`);
      return 0;
    }

    const endTime = performance.now();
    const duration = endTime - startTime;

    this.timers.delete(label);

    const performanceInfo: PerformanceInfo = {
      startTime,
      endTime,
      duration,
      memoryBefore: process.memoryUsage(),
      memoryAfter: process.memoryUsage()
    };

    this.logger.info(`Performance: ${label}`, {
      performance: performanceInfo
    });

    return duration;
  }

  /**
   * Measure the performance of an async function
   */
  async measure<T>(label: string, fn: () => Promise<T>): Promise<T> {
    const startTime = performance.now();
    const memoryBefore = process.memoryUsage();

    try {
      const result = await fn();
      const endTime = performance.now();
      const duration = endTime - startTime;
      const memoryAfter = process.memoryUsage();

      const performanceInfo: PerformanceInfo = {
        startTime,
        endTime,
        duration,
        memoryBefore,
        memoryAfter
      };

      this.logger.info(`Performance: ${label}`, {
        performance: performanceInfo
      });

      return result;
    } catch (error) {
      const endTime = performance.now();
      const duration = endTime - startTime;
      const memoryAfter = process.memoryUsage();

      const performanceInfo: PerformanceInfo = {
        startTime,
        endTime,
        duration,
        memoryBefore,
        memoryAfter
      };

      this.logger.error(`Performance: ${label} (failed)`, {
        performance: performanceInfo
      });

      throw error;
    }
  }

  /**
   * Measure the performance of a synchronous function
   */
  measureSync<T>(label: string, fn: () => T): T {
    const startTime = performance.now();
    const memoryBefore = process.memoryUsage();

    try {
      const result = fn();
      const endTime = performance.now();
      const duration = endTime - startTime;
      const memoryAfter = process.memoryUsage();

      const performanceInfo: PerformanceInfo = {
        startTime,
        endTime,
        duration,
        memoryBefore,
        memoryAfter
      };

      this.logger.info(`Performance: ${label}`, {
        performance: performanceInfo
      });

      return result;
    } catch (error) {
      const endTime = performance.now();
      const duration = endTime - startTime;
      const memoryAfter = process.memoryUsage();

      const performanceInfo: PerformanceInfo = {
        startTime,
        endTime,
        duration,
        memoryBefore,
        memoryAfter
      };

      this.logger.error(`Performance: ${label} (failed)`, {
        performance: performanceInfo
      });

      throw error;
    }
  }

  /**
   * Log memory usage information
   */
  logMemoryUsage(label: string): void {
    const memoryUsage = process.memoryUsage();
    
    this.logger.info(`Memory usage: ${label}`, {
      memoryUsage: {
        rss: `${Math.round(memoryUsage.rss / 1024 / 1024)}MB`,
        heapTotal: `${Math.round(memoryUsage.heapTotal / 1024 / 1024)}MB`,
        heapUsed: `${Math.round(memoryUsage.heapUsed / 1024 / 1024)}MB`,
        external: `${Math.round(memoryUsage.external / 1024 / 1024)}MB`
      }
    });
  }

  /**
   * Create a performance decorator for methods
   */
  createDecorator(label?: string) {
    return (target: any, propertyKey: string, descriptor: PropertyDescriptor) => {
      const originalMethod = descriptor.value;
      const perfLabel = label || `${target.constructor.name}.${propertyKey}`;

      descriptor.value = async function (...args: any[]) {
        return this.measure(perfLabel, () => originalMethod.apply(this, args));
      };

      return descriptor;
    };
  }
}
