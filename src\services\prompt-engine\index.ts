/**
 * Prompt Engine module exports
 * Core prompt enhancement, processing, and transformation capabilities
 */

// Core classes
export { PromptEngine, getPromptEngine, initializePromptEngine } from './PromptEngine';
export { AIServiceManager } from './AIServiceManager';
export { TemplateProcessor } from './TemplateProcessor';
export { ContextInjector } from './ContextInjector';
export { PromptCache } from './PromptCache';
export { ConversationManager } from './ConversationManager';

// Types and interfaces
export * from './types';

// Re-export commonly used types for convenience
export type {
  IPromptEngine,
  IAIService,
  PromptEngineConfig,
  EnhancementOptions,
  PromptTemplate,
  TemplateVariable,
  ContextSource,
  ContextData,
  ChatMessage,
  AIResponse,
  SummarizationOptions,
  ConversationContext,
  TemplateValidationResult
} from './types';

// Convenience functions
export const createPromptEngine = async (config?: any): Promise<any> => {
  return initializePromptEngine(config);
};

export const getDefaultPromptEngine = (): any => {
  return getPromptEngine();
};

// Enhancement functions
export const enhancePrompt = async (prompt: string, options?: any): Promise<string> => {
  return getPromptEngine().enhance(prompt, options);
};

export const processTemplate = async (template: any, variables: Record<string, any>): Promise<string> => {
  return getPromptEngine().processTemplate(template, variables);
};

export const injectContext = async (prompt: string, contextSources: any[]): Promise<string> => {
  return getPromptEngine().injectContext(prompt, contextSources);
};

export const generateVariations = async (prompt: string, count: number): Promise<string[]> => {
  return getPromptEngine().generateVariations(prompt, count);
};

export const summarizeText = async (text: string, options?: any): Promise<string> => {
  return getPromptEngine().summarize(text, options);
};

export const translateText = async (text: string, targetLanguage: string): Promise<string> => {
  return getPromptEngine().translate(text, targetLanguage);
};

// Template functions
export const validateTemplate = (template: string): any => {
  const processor = new TemplateProcessor();
  return processor.validate(template);
};

export const getBuiltInTemplates = (): any[] => {
  const processor = new TemplateProcessor();
  return processor.getBuiltInTemplates();
};

// Context functions
export const extractContext = async (sources: any[]): Promise<string> => {
  return getPromptEngine().injectContext('', sources);
};

// Configuration functions
export const updatePromptEngineConfig = (config: any): void => {
  return getPromptEngine().updateConfig(config);
};

export const getPromptEngineConfig = (): any => {
  return getPromptEngine().getConfig();
};

export const getPromptEngineMetrics = (): any => {
  return getPromptEngine().getMetrics();
};

// Built-in templates
export const BUILT_IN_TEMPLATES = [
  {
    id: 'code-review',
    name: 'Code Review Request',
    description: 'Generate a code review request prompt',
    template: `Please review the following {{language}} code:

\`\`\`{{language}}
{{code}}
\`\`\`

Focus on:
{{#each focusAreas}}
- {{this}}
{{/each}}

{{#if context}}
Additional context: {{context}}
{{/if}}`,
    variables: [
      {
        name: 'language',
        type: 'string',
        required: true,
        description: 'Programming language'
      },
      {
        name: 'code',
        type: 'string',
        required: true,
        description: 'Code to review'
      },
      {
        name: 'focusAreas',
        type: 'array',
        required: false,
        defaultValue: ['Performance', 'Security', 'Best Practices'],
        description: 'Areas to focus on during review'
      }
    ],
    category: 'Development',
    tags: ['code', 'review', 'development']
  },
  {
    id: 'text-summarization',
    name: 'Text Summarization',
    description: 'Summarize long text content',
    template: `Please summarize the following text in {{#if length}}{{length}}{{else}}3-5{{/if}} sentences:

{{text}}

{{#if style}}
Style: {{style}}
{{/if}}

{{#if audience}}
Target audience: {{audience}}
{{/if}}`,
    variables: [
      {
        name: 'text',
        type: 'string',
        required: true,
        description: 'Text to summarize'
      },
      {
        name: 'length',
        type: 'string',
        required: false,
        description: 'Desired summary length'
      },
      {
        name: 'style',
        type: 'string',
        required: false,
        description: 'Summary style (bullet points, paragraph, etc.)'
      }
    ],
    category: 'Text Processing',
    tags: ['summary', 'text', 'analysis']
  },
  {
    id: 'email-draft',
    name: 'Email Draft',
    description: 'Draft professional emails',
    template: `Draft a {{tone}} email with the following details:

To: {{recipient}}
Subject: {{subject}}

{{#if context}}
Context: {{context}}
{{/if}}

Key points to include:
{{#each keyPoints}}
- {{this}}
{{/each}}

{{#if callToAction}}
Call to action: {{callToAction}}
{{/if}}`,
    variables: [
      {
        name: 'recipient',
        type: 'string',
        required: true,
        description: 'Email recipient'
      },
      {
        name: 'subject',
        type: 'string',
        required: true,
        description: 'Email subject'
      },
      {
        name: 'tone',
        type: 'string',
        required: false,
        defaultValue: 'professional',
        description: 'Email tone'
      },
      {
        name: 'keyPoints',
        type: 'array',
        required: true,
        description: 'Key points to include'
      }
    ],
    category: 'Communication',
    tags: ['email', 'communication', 'professional']
  }
];

// Utility functions
export const validatePromptTemplate = (template: any): boolean => {
  try {
    if (!template.id || !template.name || !template.template) {
      return false;
    }
    if (!Array.isArray(template.variables)) {
      return false;
    }
    return true;
  } catch {
    return false;
  }
};

export const createPromptTemplate = (
  id: string,
  name: string,
  template: string,
  variables: any[] = []
): any => ({
  id,
  name,
  description: '',
  template,
  variables,
  category: 'Custom',
  tags: [],
  metadata: {
    author: 'user',
    version: '1.0.0',
    createdAt: new Date(),
    updatedAt: new Date()
  }
});

export const estimateTokens = (text: string): number => {
  // Simple estimation: roughly 4 characters per token
  return Math.ceil(text.length / 4);
};

export const calculateEnhancementScore = (original: string, enhanced: string): number => {
  // Simple scoring based on length increase and structure
  const lengthRatio = enhanced.length / original.length;
  const structureScore = enhanced.includes('\n') ? 1.2 : 1.0;
  const clarityScore = enhanced.split(' ').length > original.split(' ').length ? 1.1 : 1.0;
  
  return Math.min(lengthRatio * structureScore * clarityScore, 3.0);
};

// Configuration helpers
export const getDefaultPromptEngineConfig = () => ({
  ai: {
    provider: 'openai' as const,
    defaultModel: 'gpt-3.5-turbo',
    timeout: 30000,
    retries: 3
  },
  cache: {
    maxSize: 100 * 1024 * 1024, // 100MB
    ttl: 3600000, // 1 hour
    maxEntries: 1000
  },
  templates: {
    builtInEnabled: true,
    autoSave: true
  },
  context: {
    maxSources: 5,
    maxContentLength: 10000,
    enableSensitiveFilter: true
  },
  enhancement: {
    defaultStyle: 'professional',
    defaultLength: 'detailed',
    defaultTone: 'friendly',
    maxVariations: 5
  }
});

export const createDefaultEnhancementOptions = () => ({
  style: 'professional' as const,
  length: 'detailed' as const,
  tone: 'friendly' as const,
  temperature: 0.7,
  maxTokens: 1000,
  preserveStructure: false,
  addExamples: false,
  includeInstructions: true
});

// Context source helpers
export const getAllContextSources = () => [
  'clipboard',
  'screen-capture',
  'active-window',
  'file-content',
  'selected-text',
  'conversation-history',
  'browser-tab',
  'system-info'
];

export const getContextSourceLabel = (source: string): string => {
  const labels: Record<string, string> = {
    'clipboard': 'Clipboard Content',
    'screen-capture': 'Screen Capture',
    'active-window': 'Active Window',
    'file-content': 'File Content',
    'selected-text': 'Selected Text',
    'conversation-history': 'Conversation History',
    'browser-tab': 'Browser Tab',
    'system-info': 'System Information'
  };
  
  return labels[source] || source;
};

// Error handling helpers
export const isPromptEngineError = (error: any): boolean => {
  return error && (
    error.name === 'PromptEngineError' ||
    error.name === 'TemplateProcessingError' ||
    error.name === 'AIServiceError' ||
    error.name === 'ContextExtractionError'
  );
};

export const getErrorMessage = (error: any): string => {
  if (isPromptEngineError(error)) {
    return error.message;
  }
  return 'An unknown prompt engine error occurred';
};
